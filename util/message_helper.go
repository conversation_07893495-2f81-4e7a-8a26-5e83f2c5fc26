package utils

import (
	"fmt"
	"regexp"
)

type VCard struct {
	Phones []string
}

func ExtractVCard(vcard string) VCard {
	phones := make([]string, 0)

	r, _ := regexp.Compile(`TEL(?:;type=([a-zA-Z]+)|)(?:;waid=(\d+)|):(\+[\d\s-]+)`)
	for _, tel := range r.FindAllStringSubmatch(vcard, -1) {
		typePhone, waId, phone := tel[1], tel[2], tel[3]
		phones = append(phones, phone)
		fmt.Println(phone, "type:", typePhone, "isWa?", waId != "")
	}

	return VCard{
		Phones: phones,
	}
}
