package request

import (
	"bytes"
	"encoding/json"
	"fmt"
	utils "gitlab.com/uniqdev/backend/uniq-bot/util"
	"io"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	net "net/url"
	"os"
	"path/filepath"
	"strings"
)

type HttpRequest struct {
	Method           string
	Url              string
	Header           map[string]interface{}
	PostRequest      PostRequest
	MultipartRequest MultipartRequest
}

type PostRequest struct {
	Body interface{}
	Form map[string]string
}

type MultipartRequest struct {
	FilePath  string
	FileParam string
	Form      map[string]string
}

func (req HttpRequest) Execute() ([]byte, error) {
	return request(req)
}

func request(request HttpRequest) ([]byte, error) {
	var body string
	if len(request.PostRequest.Form) > 0 {
		data := net.Values{}
		for key, value := range request.PostRequest.Form {
			data.Set(key, value)
		}
		body = data.Encode()
		if request.Header == nil {
			request.Header = make(map[string]interface{})
		}
		request.Header["Content-Type"] = "application/x-www-form-urlencoded"
	} else {
		dataJson, err := json.Marshal(request.PostRequest.Body)
		if err != nil {
			fmt.Println("Parse map to json error : ", err)
		}
		body = string(dataJson)
	}

	var req *http.Request
	var err error

	if request.MultipartRequest.FilePath != "" {
		req, err = createMultipartRequest(request)
	} else {
		req, err = http.NewRequest(request.Method, request.Url, strings.NewReader(body))
	}

	if err != nil {
		fmt.Println("Creating http request error ", err)
		return nil, err
	}

	for key, value := range request.Header {
		req.Header.Set(key, utils.ToString(value))
	}

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("Request to '%s' error %v \n", request.Url, err)
		return nil, err
	}
	defer resp.Body.Close()

	bodyResp, err := ioutil.ReadAll(resp.Body)
	return bodyResp, err
}

func createMultipartRequest(request HttpRequest) (*http.Request, error) {
	file, err := os.Open(request.MultipartRequest.FilePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	part, err := writer.CreateFormFile(request.MultipartRequest.FileParam, filepath.Base(request.MultipartRequest.FilePath))
	if err != nil {
		return nil, err
	}
	_, err = io.Copy(part, file)

	for key, val := range request.MultipartRequest.Form {
		_ = writer.WriteField(key, val)
	}
	err = writer.Close()
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", request.Url, body)
	req.Header.Set("Content-Type", writer.FormDataContentType())
	return req, err
}
