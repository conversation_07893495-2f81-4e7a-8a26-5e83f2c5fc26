package utils

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"mime"
	"mime/multipart"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"reflect"
	"regexp"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"syscall"
	"time"
	"unicode"

	"github.com/valyala/fasthttp"
	qrcode "github.com/yeqown/go-qrcode"
)

func KillPort(port string) {
	if _, err := strconv.Atoi(port); err != nil {
		os.Stderr.WriteString(fmt.Sprintf("Error: port argument is not a number.\n"))
		os.Exit(1)
	}

	if runtime.GOOS == "windows" {
		command := fmt.Sprintf("(Get-NetTCPConnection -LocalPort %s).OwningProcess -Force", port)
		exec_cmd(exec.Command("Stop-Process", "-Id", command))
	} else {
		command := fmt.Sprintf("lsof -i tcp:%s | grep LISTEN | awk '{print $2}' | xargs kill -9", port)
		exec_cmd(exec.Command("bash", "-c", command))
	}
}

// Execute command and return exited code.
func exec_cmd(cmd *exec.Cmd) {
	var waitStatus syscall.WaitStatus
	if err := cmd.Run(); err != nil {
		if err != nil {
			os.Stderr.WriteString(fmt.Sprintf("Error: %s\n", err.Error()))
		}
		if exitError, ok := err.(*exec.ExitError); ok {
			waitStatus = exitError.Sys().(syscall.WaitStatus)
			fmt.Printf("Error during killing (exit code: %s)\n", []byte(fmt.Sprintf("%d", waitStatus.ExitStatus())))
		}
	} else {
		waitStatus = cmd.ProcessState.Sys().(syscall.WaitStatus)
		fmt.Printf("Port successfully killed (exit code: %s)\n", []byte(fmt.Sprintf("%d", waitStatus.ExitStatus())))
	}
}

func CheckErr(err error) bool {
	if err != nil {
		date := time.Unix(time.Now().Unix()+25200, 0).Format("2006-01-02 15:04:05")
		//fmt.Println(date, "   Error While ", onDoing, ". ", err)
		_, fn, line, _ := runtime.Caller(1)
		//fmt.Printf("%s  [error] %s:%d %v", date, fn, line, err)
		log.Printf("%s  [error] %s:%d %v", date, fn, line, err)
		errorMsg := fmt.Sprintf("%s  [error] %s:%d %v", date, fn, line, err)
		fmt.Println(errorMsg)

		//core.SendWhatsApp("6285742257881", "*WA Bot Error*. \n"+errorMsg)

		//send error to slack channel
		//SendNotifServerToSlack(errorMsg)

		return true
	}

	return false
}

//func SendNotifServerToSlack(message string) {
//	request := request2.HttpRequest{}
//	request.Method = "POST"
//	request.Url = "*****************************************************************************"
//	request.PostRequest.Body = map[string]interface{}{
//		"text": message,
//	}
//	_, err := request.Execute()
//	if err != nil {
//		fmt.Println("Sending to slack error ", err)
//	}
//}

func DownloadFile(filepath string, url string) error {
	// Get the data
	resp, err := http.Get(url)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// Create the file
	out, err := os.Create(filepath)
	if err != nil {
		return err
	}
	defer out.Close()

	// Write the body to file
	_, err = io.Copy(out, resp.Body)
	return err
}

func DownloadFileBase64(base64Data interface{}, savePath string) error {
	var dec []byte
	var err error

	switch v := base64Data.(type) {
	case string:
		dec, err = base64.StdEncoding.DecodeString(v)
	case []byte:
		dec = v
	default:
		err = fmt.Errorf("unrecognise base64 data type: %v", reflect.TypeOf(base64Data))
	}

	if err != nil {
		fmt.Println("decode error... base64: ", base64Data)
		return err
	}

	dir, _ := filepath.Split(savePath)
	if dir != "" {
		if _, err := os.Stat(dir); os.IsNotExist(err) {
			err = os.MkdirAll(dir, os.ModePerm)
			if err != nil {
				fmt.Println("failed to create dir : ", dir)
				return err
			}
		}
	}

	f, err := os.Create(savePath)
	if err != nil {
		return err
	}
	defer f.Close()

	if _, err := f.Write(dec); err != nil {
		return err
	}
	if err := f.Sync(); err != nil {
		return err
	}

	return nil
}

func DeleteFile(path string) {
	// delete file
	var err = os.Remove(path)
	CheckErr(err)

	fmt.Println("==> done deleting file - ", path)
}

// ======================================== VALUE CONVERTER ========================================
func StringToInt(data string) int {
	res, err := strconv.Atoi(data)
	if err != nil {
		return 0
	} else {
		return res
	}
}

func InterfaceToString(data interface{}) string {
	switch v := data.(type) {
	case int:
		return strconv.Itoa(v)
	case string:
		return v
	default:
		return ""
	}
}

func ToInt(data interface{}) int {
	dataStr := ToString(data)
	if dataStr == "" {
		return 0
	}
	result, err := strconv.Atoi(dataStr)
	if CheckErr(err) {
		return 0
	} else {
		return result
	}
}

func ToInt64(data interface{}) int64 {
	dataStr := ToString(data)
	if dataStr == "" {
		return 0
	}
	result, err := strconv.ParseInt(dataStr, 10, 64)
	if CheckErr(err) {
		return 0
	} else {
		return result
	}
}

func MillisToDateTime(timeMillis int64) (string, error) {
	t := time.Unix(timeMillis, 0)
	return t.Format("2006-01-02 15:04:05"), nil
}

func ReplaceEmoji(data string) string {
	//[^a-zA-Z0-9]+
	//[\x{1F600}-\x{1F6FF}|[\x{2600}-\x{26FF}]
	var emojiRx = regexp.MustCompile(`[^a-zA-Z0-9]+`)
	return emojiRx.ReplaceAllString(data, ` `)
}

func CurrencyFormat(amount int) string {
	result := ""
	amountStr := strconv.Itoa(amount)
	index := 1
	for i := len(amountStr); i > 0; i-- {
		data := amountStr[i-1 : i]
		if index%3 == 0 {
			result += data + "."
		} else {
			result += data
		}
		index++
	}
	result = Reverse(result)
	if strings.HasPrefix(result, ".") {
		result = result[1:]
	} else if strings.HasPrefix(result, "-.") {
		result = "-" + result[2:]
	}

	return result
}

func Reverse(s string) string {
	runes := []rune(s)
	for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
		runes[i], runes[j] = runes[j], runes[i]
	}
	return string(runes)
}

func ToString(data interface{}) string {
	switch v := data.(type) {
	case int:
		return strconv.Itoa(v)
	case int64:
		return strconv.FormatInt(v, 10)
	case uint8:
		return strconv.Itoa(int(v))
	case uint64:
		return strconv.FormatUint(v, 10)
	case []uint8:
		return strconv.Itoa(int(v[1]))
	case float64:
		return strconv.FormatFloat(v, 'f', 2, 64)
	case string:
		return v
	default:
		_, fn, line, _ := runtime.Caller(1)
		fmt.Printf("%s:%d failed converting to string : '%v' type is %v\n", fn, line, data, reflect.TypeOf(data))
		return ""
	}
}

func IsValidPhoneNumber(data string) bool {
	//r,_ := regexp.Compile(`^\d+$|(^\d+-\d+$)`)
	//return r.MatchString(ToString(ToInt64(data)))
	if len(data) < 10 {
		fmt.Printf("%s can not be phone number, it's too short", data)
		return false
	}

	if len(data) > 14 {
		//it could be group id
		if strings.Contains(data, "-") {
			//let's split the phone
			phones := strings.Split(data, "-")
			if len(phones) != 2 {
				fmt.Printf("%s can not be group phone number, after split it's not group of 2", data)
				return false
			}
		} else {
			fmt.Printf("%s can not be phone number, it's too long (%d)", data, len(data))
			return false
		}
	}

	data = strings.Replace(data, "\u202c", "", -1)
	if strings.Contains(data, "-") {
		for _, s := range strings.Split(data, "-") {
			_, err := strconv.ParseInt(s, 10, 64)
			if err != nil {
				fmt.Printf("'%s' is not number", s)
				return false
			}
		}
	} else {
		_, err := strconv.ParseInt(data, 10, 64)
		return err == nil
	}
	return true
}

func IsNumber(data string) bool {
	for _, s := range data {
		if !unicode.IsDigit(s) {
			fmt.Printf("'%c' is not number", s)
			if fmt.Sprintf("%c", s) == "  " {
				fmt.Print("empty")
			}
			return false
		}
	}
	return true
}

func GetFormValueMap(request *fasthttp.RequestCtx) map[string]string {
	values := make(map[string]string)
	mediaType, params, err := mime.ParseMediaType(string(request.Request.Header.Peek("Content-Type")))
	if err != nil || !strings.HasPrefix(mediaType, "multipart/") {
		//for i := range keys {
		//	values[keys[i]] = string(request.FormValue(keys[i]))
		//}
		fmt.Println("Request is not Multipart")
	} else { // multi form
		buf, _ := ioutil.ReadAll(bytes.NewReader(request.PostBody()))
		//origBody := ioutil.NopCloser(bytes.NewBuffer(buf))
		var rdr = multipart.NewReader(bytes.NewBuffer(buf), params["boundary"])
		for true {
			part, err_part := rdr.NextPart()
			if err_part == io.EOF {
				break
			}

			buf := new(bytes.Buffer)
			buf.ReadFrom(part)
			values[part.FormName()] = buf.String()
		}

		//request.body = origBody
	}
	return values
}

func WaitTimeout(wg *sync.WaitGroup, timeout time.Duration) bool {
	c := make(chan struct{})
	go func() {
		defer close(c)
		wg.Wait()
	}()
	select {
	case <-c:
		return false // completed normally
	case <-time.After(timeout):
		return true // timed out
	}
}

func ReadFile(path string) (string, error) {
	file, err := os.Open(path)
	if err != nil {
		return "", err
	}
	defer file.Close()
	result, err := ioutil.ReadAll(file)
	return string(result), nil
}

func SimplyToJson(data interface{}) string {
	resp, err := json.Marshal(data)
	if err == nil {
		return string(resp)
	} else {
		return "<INVALID>"
	}
}

func CreateFolders(paths ...string) error {
	for _, path := range paths {
		dir, _ := filepath.Split(path)
		if _, err := os.Stat(dir); os.IsNotExist(err) {
			err = os.MkdirAll(dir, os.ModePerm)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func GetMD5Hash(text string) string {
	hasher := md5.New()
	hasher.Write([]byte(text))
	return hex.EncodeToString(hasher.Sum(nil))
}

var iv = []byte{35, 46, 57, 24, 85, 35, 24, 74, 87, 35, 88, 98, 66, 32, 14, 05}

func Encrypt(key, text string) string {
	if len(key) < 24 {
		for len(key) < 24 {
			key += "x"
		}
	} else if len(key) > 24 {
		key = key[0:24]
	}

	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		panic(err)
	}
	plaintext := []byte(text)
	cfb := cipher.NewCFBEncrypter(block, iv)
	ciphertext := make([]byte, len(plaintext))
	cfb.XORKeyStream(ciphertext, plaintext)
	return encodeBase64(ciphertext)
}

func Decrypt(key, text string) string {
	fmt.Println(text)
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		panic(err)
	}
	ciphertext := decodeBase64(text)
	cfb := cipher.NewCFBEncrypter(block, iv)
	plaintext := make([]byte, len(ciphertext))
	cfb.XORKeyStream(plaintext, ciphertext)
	return string(plaintext)
}

func encodeBase64(b []byte) string {
	return base64.StdEncoding.EncodeToString(b)
}

func decodeBase64(s string) []byte {
	data, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		panic(err)
	}
	return data
}

func TakeMax(data string, max int) string {
	if len(data) > max {
		return data[:max]
	}
	return data
}

func GenerateQrFile(text string) (string, error) {
	qrc, err := qrcode.New(text)
	if err != nil {
		return "", err
	}

	fileName := fmt.Sprintf("qrcode_%v.jpg", time.Now().Unix())
	if err = qrc.Save(fileName); err != nil {
		return "", err
	}
	return fileName, nil
}

func FormatWhatsAppNumber(phone string) string {
	phone = strings.Replace(phone, "\u202c", "", -1)
	phone = strings.TrimSpace(phone)
	phone = strings.TrimPrefix(phone, "+")
	if strings.HasPrefix(phone, "08") {
		phone = "62" + phone[1:]
	}
	if strings.HasPrefix(phone, "008") {
		phone = "62" + strings.TrimLeft(phone, "0")
	}

	return phone
}

func IsFileExist(filepath string) bool {
	if _, err := os.Stat(filepath); errors.Is(err, os.ErrNotExist) {
		// file does not exist
		return false
	} else {
		// file exists
		return true
	}
}
