package models

type SlackMessage struct {
	Attachments []SlackAttachments `json:"attachments"`
}
type SlackFields struct {
	Title string `json:"title"`
	Value string `json:"value"`
	Short bool   `json:"short"`
}
type SlackAttachments struct {
	Fallback string        `json:"fallback"`
	Text     string        `json:"text"`
	Fields   []SlackFields `json:"fields"`
	Color    string        `json:"color"`
}
