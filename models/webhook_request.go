package models

import "time"

type WebHookRequest struct {
	ResponseID                  string      `json:"responseId"`
	QueryResult                 QueryResult `json:"queryResult"`
	OriginalDetectIntentRequest struct {
		Payload map[string]interface{} `json:"payload"`
		Source  string                 `json:"source"`
		User    UserGoogle             `json:"user"`
	} `json:"originalDetectIntentRequest"`
	Session string `json:"session"`
}

type QueryResult struct {
	QueryText                 string                 `json:"queryText"`
	Action                    string                 `json:"action"`
	Parameters                map[string]interface{} `json:"parameters"`
	AllRequiredParamsPresent  bool                   `json:"allRequiredParamsPresent"`
	Intent                    Intent                 `json:"intent"`
	IntentDetectionConfidence float64                `json:"intentDetectionConfidence"`
	DiagnosticInfo            struct {
		QuerySentimentAnalysisError string `json:"query_sentiment_analysis_error"`
	} `json:"diagnosticInfo"`
	LanguageCode   string                   `json:"languageCode"`
	OutputContexts []map[string]interface{} `json:"outputContexts"`
}

type Intent struct {
	Name        string `json:"name"`
	DisplayName string `json:"displayName"`
}

type UserGoogle struct {
	UserStorage string      `json:"userStorage"`
	LastSeen    time.Time   `json:"lastSeen"`
	Locale      string      `json:"locale"`
	UserID      string      `json:"userId"`
	Profile     UserProfile `json:"profile"`
}

type UserProfile struct {
	DisplayName string `json:"displayName"`
	GivenName   string `json:"givenName"`
	FamilyName  string `json:"familyName"`
	Email       string `json:"email"`
}
