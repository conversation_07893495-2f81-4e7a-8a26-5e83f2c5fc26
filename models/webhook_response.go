package models

type WebHookResponseBackend struct {
	FulfillmentText     string `json:"fulfillmentText"`
	FulfillmentMessages []struct {
		Card struct {
			Title    string `json:"title"`
			Subtitle string `json:"subtitle"`
			ImageURI string `json:"imageUri"`
			Buttons  []struct {
				Text     string `json:"text"`
				Postback string `json:"postback"`
			} `json:"buttons"`
		} `json:"card"`
	} `json:"fulfillmentMessages"`
	Source  string `json:"source"`
	Payload struct {
		Google struct {
			ExpectUserResponse bool `json:"expectUserResponse"`
			RichResponse       struct {
				Items []Items `json:"items"`
			} `json:"richResponse"`
			UserStorage  string       `json:"userStorage"`
			SystemIntent SystemIntent `json:"systemIntent"`
		} `json:"google"`
		Facebook struct {
			Text string `json:"text"`
		} `json:"facebook"`
		Slack struct {
			Text string `json:"text"`
		} `json:"slack"`
	} `json:"payload"`
	FollowupEventInput struct {
		Name         string `json:"name"`
		LanguageCode string `json:"languageCode"`
		Parameters   struct {
			Param string `json:"param"`
		} `json:"parameters"`
	} `json:"followupEventInput"`
}

type IntentData struct {
	Type        string   `json:"@type"`
	OptContext  string   `json:"optContext"`
	Permissions []string `json:"permissions"`
}

type SystemIntent struct {
	Intent string     `json:"intent"`
	Data   IntentData `json:"data"`
}

type Items struct {
	SimpleResponse SimpleResponse `json:"simpleResponse"`
}

type SimpleResponse struct {
	TextToSpeech string `json:"textToSpeech"`
}

type WebHookResponse struct {
	ResponseID  string `json:"responseId"`
	QueryResult struct {
		QueryText                string                 `json:"queryText"`
		Action                   string                 `json:"action"`
		Parameters               map[string]interface{} `json:"parameters"`
		AllRequiredParamsPresent bool                   `json:"allRequiredParamsPresent"`
		FulfillmentText          string                 `json:"fulfillmentText"`
		FulfillmentMessages      []struct {
			Text struct {
				Text []string `json:"text"`
			} `json:"text"`
		} `json:"fulfillmentMessages"`
		WebhookPayload struct {
			Facebook struct {
				Text string `json:"text"`
			} `json:"facebook"`
			Slack struct {
				Text string `json:"text"`
			} `json:"slack"`
			Google struct {
				ExpectUserResponse bool `json:"expectUserResponse"`
				RichResponse       struct {
					Items interface{} `json:"items"`
				} `json:"richResponse"`
			} `json:"google"`
		} `json:"webhookPayload"`
		Intent struct {
			Name        string `json:"name"`
			DisplayName string `json:"displayName"`
		} `json:"intent"`
		IntentDetectionConfidence interface{} `json:"intentDetectionConfidence"`
		DiagnosticInfo            struct {
			WebhookLatencyMs int `json:"webhook_latency_ms"`
		} `json:"diagnosticInfo"`
		LanguageCode string `json:"languageCode"`
	} `json:"queryResult"`
	WebhookStatus struct {
		Message string `json:"message"`
	} `json:"webhookStatus"`
}
