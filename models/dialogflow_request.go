package models

type DialogFlowQueryParam struct {
	QueryInput  QueryInput  `json:"queryInput"`
	QueryParams QueryParams `json:"queryParams"`
}

type QueryInput struct {
	Text Text `json:"text"`
}

type Text struct {
	Text         string `json:"text"`
	LanguageCode string `json:"languageCode"`
}

type QueryParams struct {
	TimeZone string                 `json:"timeZone"`
	Payload  map[string]interface{} `json:"payload"`
}
