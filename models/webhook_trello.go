package models

import "time"

type TrelloWebHook struct {
	Model  Model `json:"model"`
	Action struct {
		ID              string    `json:"id"`
		IDMemberCreator string    `json:"idMemberCreator"`
		Data            Data      `json:"data"`
		Type            string    `json:"type"`
		Date            time.Time `json:"date"`
		Display         struct {
			TranslationKey string `json:"translationKey"`
			Entities       struct {
				Card struct {
					Type      string `json:"type"`
					IDList    string `json:"idList"`
					ID        string `json:"id"`
					ShortLink string `json:"shortLink"`
					Text      string `json:"text"`
				} `json:"card"`
				ListBefore struct {
					Type string `json:"type"`
					ID   string `json:"id"`
					Text string `json:"text"`
				} `json:"listBefore"`
				ListAfter struct {
					Type string `json:"type"`
					ID   string `json:"id"`
					Text string `json:"text"`
				} `json:"listAfter"`
				MemberCreator struct {
					Type     string `json:"type"`
					ID       string `json:"id"`
					Username string `json:"username"`
					Text     string `json:"text"`
				} `json:"memberCreator"`
				Checkitem struct {
					Type     string `json:"type"`
					NameHTML string `json:"nameHtml"`
					ID       string `json:"id"`
					State    string `json:"state"`
					Text     string `json:"text"`
				} `json:"checkitem"`
			} `json:"entities"`
		} `json:"display"`
		MemberCreator struct {
			ID               string      `json:"id"`
			AvatarHash       string      `json:"avatarHash"`
			AvatarURL        string      `json:"avatarUrl"`
			FullName         string      `json:"fullName"`
			IDMemberReferrer interface{} `json:"idMemberReferrer"`
			Initials         string      `json:"initials"`
			Username         string      `json:"username"`
		} `json:"memberCreator"`
	} `json:"action"`
}

type Model struct {
	ID             string      `json:"id"`
	Name           string      `json:"name"`
	Desc           string      `json:"desc"`
	DescData       interface{} `json:"descData"`
	Closed         bool        `json:"closed"`
	IDOrganization string      `json:"idOrganization"`
	Pinned         bool        `json:"pinned"`
	URL            string      `json:"url"`
	ShortURL       string      `json:"shortUrl"`
	//Prefs          Prefs       `json:"prefs"`
	//LabelNames     LabelNames  `json:"labelNames"`
}

type Prefs struct {
	PermissionLevel       string `json:"permissionLevel"`
	Voting                string `json:"voting"`
	Comments              string `json:"comments"`
	Invitations           string `json:"invitations"`
	SelfJoin              bool   `json:"selfJoin"`
	CardCovers            bool   `json:"cardCovers"`
	CardAging             string `json:"cardAging"`
	CalendarFeedEnabled   bool   `json:"calendarFeedEnabled"`
	Background            string `json:"background"`
	BackgroundImage       string `json:"backgroundImage"`
	BackgroundImageScaled []struct {
		Width  int    `json:"width"`
		Height int    `json:"height"`
		URL    string `json:"url"`
	} `json:"backgroundImageScaled"`
	BackgroundTile        bool   `json:"backgroundTile"`
	BackgroundBrightness  string `json:"backgroundBrightness"`
	BackgroundBottomColor string `json:"backgroundBottomColor"`
	BackgroundTopColor    string `json:"backgroundTopColor"`
	CanBePublic           bool   `json:"canBePublic"`
	CanBeOrg              bool   `json:"canBeOrg"`
	CanBePrivate          bool   `json:"canBePrivate"`
	CanInvite             bool   `json:"canInvite"`
}

type LabelNames struct {
	Green  string `json:"green"`
	Yellow string `json:"yellow"`
	Orange string `json:"orange"`
	Red    string `json:"red"`
	Purple string `json:"purple"`
	Blue   string `json:"blue"`
	Sky    string `json:"sky"`
	Lime   string `json:"lime"`
	Pink   string `json:"pink"`
	Black  string `json:"black"`
}

type Data struct {
	Text      string `json:"text"`
	ListAfter struct {
		Name string `json:"name"`
		ID   string `json:"id"`
	} `json:"listAfter"`
	ListBefore struct {
		Name string `json:"name"`
		ID   string `json:"id"`
	} `json:"listBefore"`
	List struct {
		Name string `json:"name"`
		ID   string `json:"id"`
	} `json:"list"`
	Board struct {
		ShortLink string `json:"shortLink"`
		Name      string `json:"name"`
		ID        string `json:"id"`
	} `json:"board"`
	Card struct {
		ShortLink string `json:"shortLink"`
		IDShort   int    `json:"idShort"`
		Name      string `json:"name"`
		ID        string `json:"id"`
		IDList    string `json:"idList"`
	} `json:"card"`
	Old struct {
		IDList string `json:"idList"`
	} `json:"old"`
}
