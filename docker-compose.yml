version: '3'

services:
  app:
    image: uniq-bot
    container_name: uniq-bot
    ports:
      - "1719:1719"
    environment:
      - wa_type=official
      - GOLANG_PROTOBUF_REGISTRATION_CONFLICT=warn
    volumes:
      - /wabot:/temp/sessions
    restart: unless-stopped
    logging:
      driver: gcplogs
      # options:
      #   gcp-project: uniq-preproduction

  app-dynamic:
    image: uniq-bot
    container_name: uniq-bot-dynamic
    ports:
      - "1819:1719"
    environment:
      - wa_type=dynamic
    volumes:
      - /wabot_dynamic:/temp/sessions
    restart: unless-stopped
    logging:
      driver: gcplogs
      # options:
      #   gcp-project: uniq-preproduction

networks:
  default:
    external:
      name: uniq-network
