variables:
  GIT_SUBMODULE_STRATEGY: recursive
  APP_NAME: uniq-bot-dynamic
  IMAGE_TAG: ${CI_REGISTRY}/${CI_PROJECT_PATH}:${CI_COMMIT_REF_NAME}-latest

stages:
  - build
  - deploy

build_image:
  stage: build
  image: docker:26.1.1-alpine3.19
  services:
    - docker:26.1.1-dind-alpine3.19
  only:
    - master
    - staging
    - dev    
  script:
    - echo ${FIREBASE_SDK_CREDENTIAL} | base64 -d > config/auth/chat-support-102fc-firebase-adminsdk.json
    - echo ${G_PUBSUB_RW_KEY} | base64 -d > config/auth/pubsub_credential.json
    - cat $GCLOUD_SERVICE_ACCOUNT >  config/auth/gcloud_service_account.json
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY}
    - docker build -t ${IMAGE_TAG} --pull .
    - docker push ${IMAGE_TAG}
  after_script:
    - docker logout ${CI_REGISTRY}
  tags:
    - testing-docker


deploy_dev:
  stage: deploy
  image: docker/compose:alpine-1.29.2
  only:
    - dev
  before_script:
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY}
  script:    
    - docker pull ${IMAGE_TAG}
    - docker container rm -f uniq-bot-dynamic || true
    - docker run -d --restart unless-stopped --name uniq-bot-dynamic --network uniq-network -p 1819:1719 -v /docker/wabot_dynamic:/temp/sessions -e wa_type=dynamic -e server=dev -m="250m" --memory-swappiness=0 $IMAGE_TAG
  tags:
    - testing-docker


deploy_staging:
  stage: deploy
  image: docker/compose:alpine-1.29.2
  only:
    - staging
  before_script:
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY}
  script:
    - echo ${ENV_STAGING} | base64 -d > .env
    - docker pull ${IMAGE_TAG}
    - docker container rm -f $APP_NAME || true
    - docker run -d --restart unless-stopped --name $APP_NAME -p 1819:1719 --network uniq-network -v /docker/wabot_dynamic:/temp/sessions -e wa_type=dynamic -m="500m" --log-driver=gcplogs --env-file=.env  ${IMAGE_TAG}
  tags:
    - staging


deployProductionOfficial:
  stage: deploy
  image: docker/compose:alpine-1.29.2
  only:
    - master
#  when: manual
  before_script:
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY}
  script:
    - docker pull ${IMAGE_TAG}    
    - docker container rm -f uniq-bot || true
    - docker run -d --restart unless-stopped --name uniq-bot --network uniq-network -p 1719:1719 -v /wabot:/temp/sessions -e wa_type=official -e server=production -e GOLANG_PROTOBUF_REGISTRATION_CONFLICT=warn -m="350m" --log-driver=gcplogs $IMAGE_TAG
  tags:
    - production

deployProductionDynamic:
  stage: deploy
  image: docker/compose:alpine-1.29.2
  only:
    - master
    - master-test
#  when: manual
  before_script:
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY}
  script:
    - docker pull ${IMAGE_TAG}
    - docker container rm -f uniq-bot-dynamic || true
    - docker run -d --restart unless-stopped --name uniq-bot-dynamic --network uniq-network -p 1819:1719 -v /wabot_dynamic:/temp/sessions -e wa_type=dynamic -e server=production -e GOLANG_PROTOBUF_REGISTRATION_CONFLICT=warn -m="350m" --memory-swappiness=0 --log-driver=gcplogs $IMAGE_TAG
  tags:
    - production    
