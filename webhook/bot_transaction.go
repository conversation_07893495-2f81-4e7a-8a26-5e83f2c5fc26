package webhook

import (
	"fmt"
	"gitlab.com/uniqdev/backend/uniq-bot/core/db"
	"gitlab.com/uniqdev/backend/uniq-bot/models"
	"gitlab.com/uniqdev/backend/uniq-bot/util"
	"gitlab.com/uniqdev/backend/uniq-bot/util/constant"
	"time"
)

func TransactionIncome(payload map[string]interface{}, parameters map[string]interface{}) string {
	//fmt.Println("Payload : ", payload)
	//fmt.Println("Params : ", parameters)
	adminId := payload["admin_id"]
	email := payload["email"]
	if adminId == nil && email == nil {
		return "Maaf, akun anda tidak terdaftar pada sistem kami!"
	}

	date := parameters["date"]
	fmt.Printf("Date : '%s'", date)
	if date == nil || date == "" {
		if parameters["date-time"] != nil {
			//getDate := func() {
			//	defer func() {date = time.Now().Format(utils.StdLongYear+utils.StdZeroMonth+utils.StdZeroDay)}()
			//	date = parameters["date-time"].(map[string]interface{})["date_time"].(string)[:10]
			//}
			//getDate()
			date = parameters["date-time"].(map[string]interface{})["date_time"].(string)[:10]
		} else {
			date = time.Now().Format(constant.StdLongYear + constant.StdZeroMonth + constant.StdZeroDay)
		}
	} else {
		date = date.(string)[:10]
	}

	fmt.Printf("Get data with AdminID : %s | and Date : %s\n", adminId, date)

	if adminId == nil && email != nil {
		data, err := database.Query("SELECT admin_id from admin where email = ?", email)
		if !utils.CheckErr(err) {
			adminId = data["admin_id"]
		}
	}

	if adminId == nil {
		return "Maaf, akun anda tidak terdaftar pada sistem kami!"
	}

	dataOutlet, err := database.Query("SELECT outlet_id, name FROM outlets  WHERE admin_fkid=? LIMIT 1", adminId)
	if utils.CheckErr(err) {
		return "Maaf, terjadi kesalahan pada sistem kami"
	}

	if len(dataOutlet) <= 0 {
		return "Anda belum memiliki data outlet!"
	}

	data, err := database.Query("SELECT COALESCE(SUM(grand_total), 0) AS total FROM sales WHERE outlet_fkid=? AND FROM_UNIXTIME(time_created/1000,'%Y-%m-%d') = ?", dataOutlet["outlet_id"], date)
	if utils.CheckErr(err) {
		return ""
	}

	total := data["total"].(string)
	return fmt.Sprintf("Total pendapatan anda untuk outlet *%s* pada tanggal %s  adalah : %s", dataOutlet["name"], date, utils.CurrencyFormat(utils.StringToInt(total)))
}

func GetIncome(param models.IncomeParam) {

}
