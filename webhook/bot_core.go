package webhook

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"gitlab.com/uniqdev/backend/uniq-bot/core/auth"
	"gitlab.com/uniqdev/backend/uniq-bot/core/db"
	"gitlab.com/uniqdev/backend/uniq-bot/models"
	"golang.org/x/oauth2"
	"io/ioutil"
	"strings"
)

func DialogFlowRequest(phone, message string) (string, error) {
	phoneLocalFormat := phone
	if strings.HasPrefix(phone, "62") {
		phoneLocalFormat = "0" + phone[2:]
	}

	data, err := database.Query("SELECT * FROM admin WHERE phone=? OR phone=?", phone, phoneLocalFormat)
	if err != nil {
		fmt.Println("Getting admin data error ", err)
	}

	if len(data) <= 0 {
		return "", errors.New("admin not found")
	}

	payload := map[string]interface{}{
		"admin_id": data["admin_id"],
	}

	requestParam := models.DialogFlowQueryParam{}
	requestParam.QueryInput = models.QueryInput{Text: models.Text{Text: message, LanguageCode: "id"}}
	requestParam.QueryParams = models.QueryParams{TimeZone: "Asia/Jakarta", Payload: payload}
	jsonValue, _ := json.Marshal(requestParam)

	url := "https://dialogflow.googleapis.com/v2/projects/uniq-187911/agent/sessions/38ccb5fe-11fd-315f-4baf-56eb0091f3a9:detectIntent"

	client := auth.OauthGoogle().Client(oauth2.NoContext)
	resp, err := client.Post(url, "application/json", bytes.NewBuffer(jsonValue))

	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)

	fmt.Println(">> Body : ", string(body))
	dialogFlowResp := models.WebHookResponse{}
	err = json.Unmarshal(body, &dialogFlowResp)
	if err != nil {
		fmt.Println("Parsing json from webhook response error. ", err)
		return "", errors.New("parsing json response from webhook error")
	}

	fmt.Println("Fulfilmen text : ", dialogFlowResp.QueryResult.FulfillmentText)

	if dialogFlowResp.QueryResult.Action != "input.unknown" {
		return dialogFlowResp.QueryResult.FulfillmentText, nil
	} else {
		return "", errors.New("bot doesn't understand")
	}
}
