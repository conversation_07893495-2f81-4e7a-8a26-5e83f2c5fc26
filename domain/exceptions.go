package domain

const (
	NotLoggedIn  = "user not loggedIn"
	CodeNotLogIn = 101
)

type SessionException struct {
	Code int
}

func (e SessionException) Error() string {
	return e.Message()
}

func (e SessionException) Message() string {
	if e.Code == CodeNotLogIn {
		return "user not authenticated"
	}
	return "failed"
}

type ResponseError struct {
	Code    int
	Message string
}

func (e ResponseError) Error() string {
	return e.Message
}
