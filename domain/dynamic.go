package domain

type Dynamic struct {
}

type MessageAttachment struct {
	Attachment     string
	AttachmentType string
}

type LoginStatus struct {
	Status string `json:"status"`
	Error  string `json:"error"`
	Phone  string `json:"phone"`
}

type DynamicUseCase interface {
	Login(id string) (map[string]interface{}, error)
	SendMessage(id string, phone string, message string, attachment MessageAttachment) error
	Logout(id string) error
	CheckStatus(qr string) map[string]interface{}
	CheckLoginStatus(id string) (bool, error)
}

type DynamicService interface {
}
