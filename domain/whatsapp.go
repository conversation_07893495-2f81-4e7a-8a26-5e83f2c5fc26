package domain

import "gitlab.com/uniqdev/backend/uniq-bot/models"

type WhatsApp struct {
	Phone          string
	Message        string
	QuotedId       string
	AttachmentPath string
}

type WhatsAppUseCase interface {
	SendMessage(phone, message, attachmentPath, quotedId string) (interface{}, error)
}

type WhatsAppService interface {
	SendWhatsAppMessage(whatsApp WhatsApp) (models.MessageResponse, error)
	Login(id string, qrChan chan string, status chan LoginStatus) error
	GetSession(id string) (WhatsAppService, error)
	Logout(id string) error

	SetDeviceName(name string)
}
