# build stage
FROM golang:alpine AS builder
#RUN apk --no-cache add build-base git bzr mercurial gcc
RUN apk add --update gcc musl-dev
# RUN apk add mpc1-dev

WORKDIR /src
# COPY go-whatsapp/go.mod go-whatsapp/go.mod
# COPY go-whatsapp/go.sum go-whatsapp/go.sum
COPY go.mod .
COPY go.sum .
# RUN GO111MODULE=on go mod download
RUN GO111MODULE=on go mod download
COPY . .
#RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o app-wa server.go
# RUN CGO_ENABLED=1 GOOS=linux GOARCH=amd64 go build -ldflags "-X google.golang.org/protobuf/reflect/protoregistry.conflictPolicy=warn" -o app-wa server.go
RUN CGO_ENABLED=1 GOOS=linux GOARCH=amd64 go build -o app-wa server.go

#final stage
FROM alpine
COPY config/ /config
COPY --from=builder src/app-wa ./
CMD ["./app-wa"]
