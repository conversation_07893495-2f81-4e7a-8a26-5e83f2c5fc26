package main

import (
	"fmt"
	"log"
	"os"

	"cloud.google.com/go/profiler"
	"github.com/joho/godotenv"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/uniq-bot/core"
	"gitlab.com/uniqdev/backend/uniq-bot/core/google"
	log2 "gitlab.com/uniqdev/backend/uniq-bot/core/log"
	httpDynamic "gitlab.com/uniqdev/backend/uniq-bot/module/dynamic/delivery/http"
	"gitlab.com/uniqdev/backend/uniq-bot/module/dynamic/usecase"
	"gitlab.com/uniqdev/backend/uniq-bot/module/service/whatsmeow"
	httpOfficial "gitlab.com/uniqdev/backend/uniq-bot/module/whatsapp/delivery/http"
	usecaseWhatsApp "gitlab.com/uniqdev/backend/uniq-bot/module/whatsapp/usecase"
	"gitlab.com/uniqdev/backend/uniq-bot/routers"
	utils "gitlab.com/uniqdev/backend/uniq-bot/util"
)

func main() {
	//utils.KillPort(port)
	e := godotenv.Load() //Load .env file
	if e != nil {
		fmt.Print(e)
	}

	cfg := google.Profiler()
	if err := profiler.Start(cfg); err != nil {
		fmt.Println(">> start profiler err: ", err)
	}

	//create necessary folders
	log2.IfError(utils.CreateFolders(core.WaSessionPathFormat, core.WaDocTmpPath))

	//<-time.After(3 * time.Second)

	// certPath := os.Getenv("ssl_cert")
	// keyPath := os.Getenv("ssl_key")
	// port := os.Getenv("port")

	//go controller.SchedulePendingMessage()
	port := os.Getenv("PORT")
	if port == "" {
		port = "1719"
	}

	log2.Info("running on port : %s | wa type : '%s' | env: ", port, os.Getenv("wa_type"), os.Getenv("server"))

	router := routers.CreateRoutes()

	//service: library which handle sending message
	//waService := gowhatsapp.NewGoWhatsAppService()
	waService := whatsmeow.NewWhatsMeowService()

	if os.Getenv("wa_type") == "dynamic" {
		fmt.Println("--- whatsapp dynamic ---")
		dynamicCase := usecase.NewDynamicUseCase(waService)
		httpDynamic.NewHttpDynamicRouter(router, dynamicCase)
	} else {
		fmt.Println("--- whatsapp official ---")
		waUseCase := usecaseWhatsApp.NewWhatsAppUseCase(waService)
		httpOfficial.NewHttpWhatsappRouter(router, waUseCase)
	}

	//fasthttp.ListenAndServeTLS(":8900", certPath, keyPath, router.Handler)
	log.Fatal(fasthttp.ListenAndServe(":"+port, router.Handler))
	// log.Fatal(fasthttp.ListenAndServeTLS(":"+port, certPath, keyPath, router.Handler))
}
