package controller

import (
	"io/ioutil"
	"strings"
	"time"

	// "github.com/Rhymen/go-whatsapp"

	"gitlab.com/uniqdev/backend/uniq-bot/core/log"
	"gitlab.com/uniqdev/backend/uniq-bot/models"
	utils "gitlab.com/uniqdev/backend/uniq-bot/util"
)

var qrLogin = make(map[string]map[string]interface{})
var latestWaVersion = models.Version{Major: 2, Minor: 2147, Patch: 16}

const waVersionLogPath = "/temp/sessions/wa_version.txt"

func addQrLoginStatus(qr string) {
	qrLogin[qr] = map[string]interface{}{
		"start":  time.Now().Unix() * 1000,
		"status": "waiting",
	}
}

func updateQrLoginData(qr, key string, data interface{}) {
	qrLogin[qr][key] = data
}

func getQrLoginStatus(qr string) map[string]interface{} {
	log.Info("qr     : %s", qr)
	return qrLogin[qr]
}

// func GetStatusByQr(ctx *fasthttp.RequestCtx) {
// 	ctx.SetContentType("application/json")
// 	qr := ctx.QueryArgs().Peek("qr")

// 	if qr == nil {
// 		qr = ctx.PostArgs().Peek("qr")
// 	}

// 	if qr == nil {
// 		ctx.SetStatusCode(fasthttp.StatusBadRequest)
// 		log.Warn("GetStatusByQr : Qr Is Empty")
// 		return
// 	}

// 	log.Info("data : %v", qrLogin)
// 	log.Info("qr   : %s", string(qr))
// 	result := getQrLoginStatus(string(qr))
// 	log.Info("result : %v", result)
// 	if result["start"] == nil {
// 		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "qr not found"})
// 		return
// 	}

// 	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: result})
// 	//_ = json.NewEncoder(ctx).Encode(result)
// }

// func GetStatus(ctx *fasthttp.RequestCtx) {
// 	ctx.SetContentType("application/json")
// 	id := ctx.UserValue("id")

// 	if _, err := os.Stat(fmt.Sprintf(core.WaSessionPathFormat, id)); os.IsNotExist(err) {
// 		log.Info("user %s not login, because file session is not exist", id)
// 		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "user not logged in", Code: 101})
// 		return
// 	}

// 	resp := make(chan models.ApiResponse)

// 	timeStart := time.Now()
// 	go func(resp chan models.ApiResponse) {
// 		result := models.ApiResponse{}
// 		defer func() { resp <- result }()

// 		waConn, err := whatsapp.NewConn(7 * time.Second)
// 		if log.IfError(err) {
// 			result.Message = "internal server error"
// 			return
// 		}

// 		//TODO: set client version
// 		waConn.SetClientVersion(0, 4, 1307)
// 		//err = waConn.SetClientName("UNIQ Client", "www.uniq.id", "10.15.4")
// 		log.IfError(err)

// 		//login
// 		session, err := readSession(utils.ToString(id))
// 		log.Info("read session took : %v", time.Since(timeStart))
// 		if !log.IfError(err) {
// 			session, err = waConn.RestoreWithSession(session)
// 			log.Info("restore session took : %v", time.Since(timeStart))
// 			if err != nil {
// 				log.Info("restoring user %s failed: %v\n", id, err)
// 				result.Code = 103
// 				result.Message = "reading user session failed, user might be logged out or offline"

// 				if !strings.Contains(err.Error(), "timed out") {
// 					//err = os.Remove(fmt.Sprintf(core.WaSessionPathFormat, id))
// 					result.Code = 105
// 					result.Message = "user already logged out"
// 				}
// 			} else {
// 				log.Info("restoring user %s session success | is connected : %v", id, waConn.Info.Connected)

// 				//log.Info(utils2.SimplyToJson(waConn.Info))
// 				//{"Battery":59,"Platform":"android","Connected":true,"Pushname":"Annas","Wid":"<EMAIL>","Lc":"US","Phone":{"Mcc":"510","Mnc":"001","OsVersion":"9","DeviceManufacturer":"Xiaomi","DeviceModel":"wayne","OsBuildNumber":"PKQ1.180904.001","WaVersion":"2.19.308"},"Plugged":true,"Tos":0,"Lg":"en","Is24h":false}

// 				data := make(map[string]interface{})
// 				data["phone"] = strings.Replace(waConn.Info.Wid, "@c.us", "", -1)
// 				data["name"] = waConn.Info.Pushname

// 				result.Status = true
// 				result.Message = "user logged in and connected"
// 				result.Data = data
// 			}
// 		} else {
// 			log.Warn("restore session failed : %v", err)
// 			result.Code = 102
// 			result.Message = "restoring user session failed"
// 		}
// 		log.Info("finish....")
// 	}(resp)

// 	result := models.ApiResponse{}
// 	select {
// 	case result = <-resp:
// 		fmt.Println("success...")
// 	case <-time.After(10 * time.Second):
// 		result.Code = 104
// 		result.Message = "getting user status timeout"
// 	}

// 	log.Info("took : %v", time.Since(timeStart))
// 	log.Info("result : %s", utils.SimplyToJson(result))
// 	_ = json.NewEncoder(ctx).Encode(result)
// }

// func GetQrCode(ctx *fasthttp.RequestCtx) {
// 	ctx.SetContentType("application/json")
// 	id := ctx.UserValue("id")
// 	qr := make(chan string)
// 	qrTmp := make(chan string)
// 	log.Info("request qr code... %s", id)

// 	//if id == nil {
// 	//	ctx.SetStatusCode(fasthttp.StatusBadRequest)
// 	//	return
// 	//}

// 	timeOut := 20 * time.Second
// 	go func(qr, qrTmp chan string) {
// 		log.Info("create new conn...")
// 		wac, err := initWaDynamic()
// 		if log.IfErrorSetStatus(ctx, err) {
// 			return
// 		}

// 		log.Info("login...")
// 		session, err := wac.Login(qr)
// 		log.Info("login finish... err: %v", err)
// 		qrUser := <-qrTmp
// 		if err != nil {
// 			log.Warn("Login user %s error - %v", id, err)
// 			updateQrLoginData(qrUser, "error", err.Error())
// 			updateQrLoginData(qrUser, "status", "failed")
// 		} else {
// 			log.Info("login success... %s | phone : %s | qr : %s", id, wac.Info.Wid, qrUser)
// 			if id == nil {
// 				id = strings.Replace(wac.Info.Wid, "@c.us", "", -1)
// 			}

// 			updateQrLoginData(qrUser, "phone", id)
// 			updateQrLoginData(qrUser, "status", "success")
// 			updateLatestWaVersion(wac)

// 			//if current session exist, logged it out
// 			if _, err := os.Stat(fmt.Sprintf(core.WaSessionPathFormat, id)); !os.IsNotExist(err) {
// 				log.Info("user %s has another session, logout it first...", id)
// 				logoutWhatsApp(id)
// 			}

// 			err = writeSession(session, utils.ToString(id))
// 			log.IfError(err)
// 		}
// 	}(qr, qrTmp)

// 	qrCode := <-qr
// 	log.Info("your qr (%s) : %s", id, qrCode)

// 	go func() {
// 		qrTmp <- qrCode
// 	}()

// 	params := url.Values{}
// 	params.Add("cht", "qr")
// 	params.Add("chs", "250x250")
// 	params.Add("chl", qrCode)

// 	baseUrl, err := url.Parse("https://chart.googleapis.com")
// 	log.IfError(err)
// 	baseUrl.Path = "chart"
// 	baseUrl.RawQuery = params.Encode()

// 	resultMap := map[string]interface{}{
// 		"expired":       time.Now().Add(timeOut).Unix() * 1000,
// 		"qr_code":       qrCode,
// 		"qr_code_image": baseUrl.String(),
// 	}

// 	addQrLoginStatus(qrCode)

// 	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: resultMap})
// }

// func updateLatestWaVersion(wac *whatsapp.Conn) {
// 	v := wac.GetClientVersion()
// 	clientVersion := &models.Version{Major: v[0], Minor: v[1], Patch: v[2]}
// 	fmt.Printf("Client has version %d.%d.%d\n", clientVersion.Minor, clientVersion.Minor, clientVersion.Patch)

// 	// v, err := whatsapp.CheckCurrentServerVersion()
// 	serverVersion := &models.Version{Major: v[0], Minor: v[1], Patch: v[2]}
// 	if err != nil {
// 		fmt.Println(err)
// 		return
// 	}
// 	fmt.Printf("Server has version %d.%d.%d\n", serverVersion.Minor, serverVersion.Minor, &serverVersion)

// 	latestWaVersion.Major = serverVersion.Major
// 	latestWaVersion.Minor = serverVersion.Minor
// 	latestWaVersion.Patch = serverVersion.Patch

// 	err = ioutil.WriteFile(waVersionLogPath, []byte(fmt.Sprintf("%d.%d.%d", serverVersion.Major, serverVersion.Minor, serverVersion.Patch)), 0644)
// }

// func LogoutFromWa(ctx *fasthttp.RequestCtx) {
// 	id := ctx.UserValue("id")

// 	if _, err := os.Stat(fmt.Sprintf(core.WaSessionPathFormat, id)); os.IsNotExist(err) {
// 		log.Info("user %s not login, because file session is not exist", id)
// 		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "user not logged in"})
// 		return
// 	}

// 	go logoutWhatsApp(id)

// 	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "logout success"})
// }

// func AddLogin(ctx *fasthttp.RequestCtx) {
// 	id := ctx.UserValue("id")

// 	//TODO: set client version
// 	wac, err := whatsapp.NewConn(20 * time.Second)
// 	wac.SetClientVersion(0, 4, 1307)
// 	//err = wac.SetClientName("UNIQ Client", "www.uniq.id", "10.15.4")
// 	log.IfError(err)

// 	qr := make(chan string)

// 	go func() {
// 		qrCode := <-qr
// 		terminal := qrcodeTerminal.New()
// 		terminal.Get(qrCode).Print()
// 		fmt.Println("scan this qr code : ", qrCode)
// 	}()

// 	session, err := wac.Login(qr)
// 	if err != nil {
// 		fmt.Errorf("error during login: %v\n", err)
// 	}

// 	err = writeSession(session, utils.ToString(id))
// 	if utils.CheckErr(err) {
// 		fmt.Errorf("error saving session: %v\n", err)
// 	}
// }

// func writeSession(session whatsapp.Session, id string) error {
// 	file, err := os.Create(fmt.Sprintf(core.WaSessionPathFormat, id))
// 	if err != nil {
// 		return err
// 	}
// 	defer file.Close()
// 	encoder := gob.NewEncoder(file)
// 	err = encoder.Encode(session)
// 	if utils.CheckErr(err) {
// 		return err
// 	}
// 	return nil
// }

// func SendMessageDynamic(ctx *fasthttp.RequestCtx) {
// 	ctx.SetContentType("application/json")
// 	id := ctx.UserValue("id")
// 	phone := string(ctx.PostArgs().Peek("phone"))
// 	message := string(ctx.PostArgs().Peek("message"))

// 	log.Info("sending dynamic message using id : %s", id)
// 	if _, err := os.Stat(fmt.Sprintf(core.WaSessionPathFormat, id)); os.IsNotExist(err) {
// 		log.Info("user %s not login, because file session is not exist", id)
// 		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 101, Message: "user not logged in"})
// 		return
// 	}

// 	phone = strings.Replace(phone, "\u202c", "", -1)
// 	if strings.HasPrefix(phone, "08") {
// 		phone = "62" + phone[1:]
// 	}

// 	log.Info("sending to '%s' | len %d", phone, len(phone))
// 	if strings.HasPrefix(phone, "+") {
// 		phone = phone[1:]
// 	}
// 	if !utils.IsValidPhoneNumber(phone) {
// 		ctx.SetStatusCode(fasthttp.StatusBadRequest)
// 		log.Info("User sending non phone number : %s - %d", phone, len(phone))
// 		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 100, Message: "invalid phone number"})
// 		return
// 	}

// 	waConn, err := initWaDynamic()
// 	if err != nil {
// 		log.Warn("[dynamic] failed to create new connection - %v", err)
// 		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "create connection failed"})
// 		return
// 	}

// 	defer func() {
// 		_, err = waConn.Disconnect()
// 		log.IfError(err)
// 	}()

// 	//login
// 	session, err := readSession(utils.ToString(id))
// 	if !log.IfError(err) {
// 		session, err = waConn.RestoreWithSession(session)
// 		if err != nil {
// 			//os.Remove("config/whatsappSession.gob")
// 			//fmt.Println("Delete session : ", "config/whatsappSession.gob")
// 			log.Info("restoring failed: %v\n", err)
// 			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 102, Message: "restoring session failed"})
// 			return
// 		}
// 		fmt.Println("restoring session success")
// 	} else {
// 		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: 103, Message: "read session failed"})
// 		return
// 	}

// 	msg := whatsapp.TextMessage{
// 		Info: whatsapp.MessageInfo{
// 			RemoteJid: phone + "@s.whatsapp.net",
// 		},
// 		Text: message,
// 	}

// 	_, err = waConn.Send(msg)
// 	if !log.IfError(err) {
// 		log.Info("[dynamic] sending message to '%s' with id '%s' success...", phone, id)
// 		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
// 	} else {
// 		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: "we have problem in sending message"})
// 	}
// 	//_,err = waConn.Disconnect()
// 	//log.IfError(err)
// }

func getClientVersion() (int, int, int) {
	//get from file if exist
	versionLog, err := ioutil.ReadFile(waVersionLogPath)
	if err == nil {
		versions := strings.Split(string(versionLog), ".")
		if len(versions) == 3 {
			latestWaVersion.Major = utils.ToInt(versions[0])
			latestWaVersion.Minor = utils.ToInt(versions[1])
			latestWaVersion.Patch = utils.ToInt(versions[2])
		}
	}
	return latestWaVersion.Major, latestWaVersion.Minor, latestWaVersion.Patch
}

// func initWaDynamic() (*whatsapp.Conn, error) {
// 	waConn, err := whatsapp.NewConn(30 * time.Second)
// 	if err != nil {
// 		return nil, err
// 	}

// 	ma, mi, pa := getClientVersion()
// 	//TODO: set client version
// 	waConn.SetClientVersion(ma, mi, pa)
// 	//err = waConn.SetClientName("UNIQ Client", "www.uniq.id", fmt.Sprintf("%d.%d.%d", ma, mi, pa))
// 	return waConn, err
// }

// func readSession(id string) (whatsapp.Session, error) {
// 	session := whatsapp.Session{}
// 	sessionFile := fmt.Sprintf(core.WaSessionPathFormat, id)

// 	file, err := os.Open(sessionFile)
// 	if log.IfError(err) {
// 		return session, err
// 	}
// 	defer file.Close()
// 	decoder := gob.NewDecoder(file)
// 	err = decoder.Decode(&session)
// 	if log.IfError(err) {
// 		return session, err
// 	}
// 	return session, nil
// }

// func logoutWhatsApp(id interface{}) {
// 	waConn, err := initWaDynamic()
// 	log.IfError(err)

// 	session, err := readSession(utils.ToString(id))
// 	if !log.IfError(err) {
// 		session, err = waConn.RestoreWithSession(session)
// 		if err != nil {
// 			log.Info("[LOGOUT] restoring failed: %v\n", err)
// 		}
// 		fmt.Println("[LOGOUT] restoring session success")
// 		log.IfError(waConn.Logout())
// 	} else {
// 		log.Info("[LOGOUT] read session failed")
// 	}
// 	log.IfError(os.Remove(fmt.Sprintf(core.WaSessionPathFormat, id)))
// }
