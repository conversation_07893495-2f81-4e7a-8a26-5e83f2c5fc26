package controller

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	net "net/url"
	"strings"

	"github.com/valyala/fasthttp"
	database "gitlab.com/uniqdev/backend/uniq-bot/core/db"
	"gitlab.com/uniqdev/backend/uniq-bot/models"
	utils "gitlab.com/uniqdev/backend/uniq-bot/util"
	"gitlab.com/uniqdev/backend/uniq-bot/util/constant"
	"gitlab.com/uniqdev/backend/uniq-bot/webhook"
)

func WebHookHandler(ctx *fasthttp.RequestCtx) {
	fmt.Println("Json Body : \n" + string(ctx.PostBody()))

	var dialogFlowReq models.WebHookRequest
	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&dialogFlowReq)
	if err != nil {
		fmt.Println("Decode request to json failed!")
	}

	message := "<PERSON><PERSON>, kami tidak mengerti apa yang anda maksud!"
	resp := models.WebHookResponseBackend{}

	if dialogFlowReq.QueryResult.Action == "transaction.income" {
		if dialogFlowReq.OriginalDetectIntentRequest.Source == "google" {
			userGoogle, _ := (dialogFlowReq.OriginalDetectIntentRequest.Payload["user"]).(map[string]interface{})
			db := database.GetDbJson()
			var user models.UserProfile
			err = db.Read(constant.DB_USER_GOOGLE, utils.ToString(userGoogle["userId"]), &user)
			fmt.Println("user (db)", user, err, userGoogle)
			if err != nil || user.Email == "" {
				resp.Payload.Google.UserStorage = "{\"data\":{}}"
				resp.Payload.Google.SystemIntent.Intent = "actions.intent.PERMISSION"
				resp.Payload.Google.SystemIntent.Data.Permissions = []string{"NAME", "DEVICE_PRECISE_LOCATION", "EMAIL"}
				resp.Payload.Google.SystemIntent.Data.OptContext = "Agar kami dapat memberikan data yang anda minta"
				resp.Payload.Google.SystemIntent.Data.Type = "type.googleapis.com/google.actions.v2.PermissionValueSpec"
				resp.Payload.Google.ExpectUserResponse = true
			} else {
				message = webhook.TransactionIncome(map[string]interface{}{
					"email": user.Email,
				}, dialogFlowReq.QueryResult.Parameters)
			}
		} else {
			message = webhook.TransactionIncome(dialogFlowReq.OriginalDetectIntentRequest.Payload, dialogFlowReq.QueryResult.Parameters)
		}
	} else if dialogFlowReq.QueryResult.QueryText == "actions_intent_PERMISSION" {
		user, _ := (dialogFlowReq.OriginalDetectIntentRequest.Payload["user"]).(map[string]interface{})
		profile, ok := (user["profile"]).(map[string]interface{})
		fmt.Println("Profile : ", profile)
		if ok && profile["email"] != "" {
			db := database.GetDbJson()
			err = db.Write(constant.DB_USER_GOOGLE, utils.ToString(user["userId"]), profile)
			utils.CheckErr(err)

			var param map[string]interface{}
			if len(dialogFlowReq.QueryResult.OutputContexts) > 0 {
				param, _ = (dialogFlowReq.QueryResult.OutputContexts[0]["parameters"]).(map[string]interface{})
			}

			message = webhook.TransactionIncome(map[string]interface{}{
				"email": dialogFlowReq.OriginalDetectIntentRequest.User.Profile.Email,
			}, param)
		} else {
			message = "Maaf, kami tidak dapat mengenali anda!"
		}
	}

	fmt.Println("result : ", message)

	resp.FulfillmentText = message
	googleResp := []models.Items{{SimpleResponse: models.SimpleResponse{TextToSpeech: message}}}
	resp.Payload.Google.RichResponse.Items = googleResp

	//defaultResp := `{"fulfillmentText":"Hello","fulfillmentMessages":null,"source":"","payload":{"google":{"expectUserResponse":false,"richResponse":{"items":[{"simpleResponse":{"textToSpeech":"Hello"}}]}},"facebook":{"text":""},"slack":{"text":""}},"followupEventInput":{"name":"","languageCode":"","parameters":{"param":""}}}`
	respMap := make(map[string]interface{})

	respJson, _ := json.Marshal(resp)

	err = json.Unmarshal(respJson, &respMap)
	if err != nil {
		fmt.Println("Errror - ", err)
	}

	json.NewEncoder(ctx).Encode(respMap)
}

// func WebHookTrello(ctx *fasthttp.RequestCtx) {
// 	ctx.SetStatusCode(fasthttp.StatusOK)
// 	fmt.Printf("\nMethod : %s | Project : %s", string(ctx.Method()), ctx.UserValue("project").(string))
// 	if string(ctx.Method()) != "POST" {
// 		return
// 	}

// 	//fmt.Println("\n>> BODY : ", string(ctx.PostBody()))
// 	var trello models.TrelloWebHook
// 	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&trello)
// 	if err != nil {
// 		fmt.Println("Read json from Trello Error. ", err)
// 		return
// 	}

// 	message := ""
// 	if trello.Action.Display.TranslationKey == "action_move_card_from_list_to_list" {
// 		if !strings.HasPrefix(strings.ToLower(trello.Action.Display.Entities.ListAfter.Text), "to do") {
// 			message = fmt.Sprintf("*#Project Update - Trello* \n%s Updated task status from *%s* to *%s*. \n\nTask : %s \nProject : %s",
// 				trello.Action.MemberCreator.FullName, trello.Action.Display.Entities.ListBefore.Text,
// 				trello.Action.Display.Entities.ListAfter.Text, trello.Action.Data.Card.Name,
// 				trello.Action.Data.Board.Name)
// 		}
// 	} else if trello.Action.Type == "updateCheckItemStateOnCard" {
// 		//only notif if checked the checklist
// 		if trello.Action.Display.TranslationKey == "action_completed_checkitem" {
// 			message = fmt.Sprintf("*#Project Update - Trello* \n%s %s Sub-Task *%s* \n\nTask : %s \nProject : %s",
// 				trello.Action.MemberCreator.FullName, trello.Action.Display.Entities.Checkitem.State,
// 				trello.Action.Display.Entities.Checkitem.Text, trello.Action.Data.Card.Name,
// 				trello.Action.Data.Board.Name)
// 		}
// 	} else if trello.Action.Display.TranslationKey == "action_create_card" {
// 		if strings.HasPrefix(strings.ToLower(trello.Action.Data.List.Name), "bug") {
// 			message = fmt.Sprintf("*#Bug Report - Trello* \n%s added list to %s \nBug : %s \nProject : %s",
// 				trello.Action.MemberCreator.FullName, trello.Action.Data.List.Name, trello.Action.Data.Card.Name,
// 				trello.Action.Data.Board.Name)
// 		}
// 	} else if trello.Action.Display.TranslationKey == "action_comment_on_card" {
// 		comment := trello.Action.Data.Text
// 		r, _ := regexp.Compile("(\\s|^)@[a-z0-9_]+")
// 		mentions := r.FindAllString(comment, -1)
// 		if len(mentions) == 0 {
// 			fmt.Println("card comment with no mention...")
// 			return
// 		}

// 		memberJson, err := utils.ReadFile("config/members.json")
// 		if err != nil {
// 			fmt.Println("error reading members data : ", err)
// 			return
// 		}

// 		var members []map[string]interface{}
// 		err = json.Unmarshal([]byte(memberJson), &members)
// 		if err != nil {
// 			fmt.Println("error decoding member json to struct : ", err)
// 			return
// 		}

// 		log.Info("mentions : %v", mentions)

// 		userNames := make(map[string]string)
// 		for _, username := range mentions {
// 			for _, member := range members {
// 				if utils.ToString(member["username"]) == strings.Replace(strings.TrimSpace(username), "@", "", 1) {
// 					userNames[username] = utils.ToString(member["phone"])
// 					break
// 				}
// 			}
// 		}

// 		if len(userNames) == 0 {
// 			fmt.Println("no members found!")
// 			return
// 		}

// 		msg := fmt.Sprintf("%s mentioned you on card *%s* \nClick to Reply -> _https://trello.com/c/%s_ \n\n*\"*%s*\"*",
// 			trello.Action.MemberCreator.FullName, trello.Action.Data.Card.Name, trello.Action.Data.Card.ShortLink,
// 			trello.Action.Data.Text)
// 		for _, phone := range userNames {
// 			_,err = core.SendWhatsApp(phone, msg, "")
// 			if err != nil {
// 				fmt.Println("sending whatsapp error : ", err)
// 			}
// 		}
// 	}

// 	if message != "" {
// 		//fmt.Println(message)
// 		//"6285742257881-1495197570 : tech
// 		//6287838362747-1495704774 : all
// 		_,_ = core.SendWhatsApp("6285742257881-1495197570", message, "")
// 	}

// 	//core.SendWhatsApp("","")
// }

func WebHookTrelloNewBoard(ctx *fasthttp.RequestCtx) {
	ctx.SetStatusCode(fasthttp.StatusOK)
	//fmt.Printf("[NEW BOARD] - Method : %s \n", string(ctx.Method()))
	if string(ctx.Method()) != "POST" {
		return
	}

	fmt.Println("\nTrello Organization WebHook Triggered")
	//fmt.Println(">> BODY : ", string(ctx.PostBody()), "\n.")
	var trello models.TrelloWebHook
	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&trello)
	if err != nil {
		fmt.Println("Read json from Trello Error. ", err)
		return
	}

	if trello.Action.Display.TranslationKey == "action_add_to_organization_board" {
		fmt.Println("New Board : ")
		//send new board to Trello
		data := net.Values{}
		data.Set("description", "UNIQ - "+trello.Action.Data.Board.Name)
		data.Set("callbackURL", "http://bot.uniq.id/webhook/trello/"+strings.Replace(trello.Action.Data.Board.Name, " ", "_", -1))
		data.Set("idModel", trello.Action.Data.Board.ID)

		url := "https://api.trello.com/1/tokens/b612f55ca8cd6c8f57ba877bfbaebe54a1f47aba12679a2f68796676f6aca382/webhooks/?key=bba29427f139820e0911e84980873e7b"
		res, err := http.PostForm(url, data)
		if err != nil {
			fmt.Println("Error ", err)
		}

		defer res.Body.Close()

		body, err := ioutil.ReadAll(res.Body)
		utils.CheckErr(err)
		fmt.Printf("[Response on adding new board] Status : %d \nJson : %s\n\n", res.StatusCode, string(body))
	} else {
		fmt.Println("Action : " + trello.Action.Display.TranslationKey)
	}
}
