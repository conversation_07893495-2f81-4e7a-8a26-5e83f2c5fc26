package controller

// func LoginOfficialWhatsApp(ctx *fasthttp.RequestCtx) {
// 	result := core.LoginOfficialAsync()
// 	//log.ToSlack("LOGIN INFO : \n"+utils.SimplyToJson(result))
// 	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Code: 0, Data: result})
// }

// func LoginStatus(ctx *fasthttp.RequestCtx) {
// 	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Code: 0, Data: core.WhatsAppLoginStatus()})
// }

// func RestartSession(ctx *fasthttp.RequestCtx) {
// 	token := ctx.UserValue("token")
// 	if token == nil {
// 		ctx.SetStatusCode(fasthttp.StatusUnauthorized)
// 		return
// 	}

// 	err := core.RestartWhatsApp(utils.ToString(token))
// 	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: err == nil, Code: 0, Message: err.Error()})
// }

// func PostMessage(ctx *fasthttp.RequestCtx) {
// 	phone := string(ctx.FormValue("phone"))
// 	msg := string(ctx.FormValue("message"))
// 	quotedId := string(ctx.FormValue("quotedId"))

// 	ipAddress := utils.FromRequest(ctx)
// 	if !auth.IsLocalOrPrivateIp(ipAddress) && ipAddress != "127.0.0.1" && !auth.IsIPAllowed(ctx) {
// 		localIP := utils.GetOutboundIP()
// 		fmt.Println("IP Address Client : ", ipAddress)
// 		fmt.Println("Your Local IP : ", localIP.String())

// 		authHeader := string(ctx.Request.Header.Peek("Authorization"))
// 		if authHeader != "" {
// 			dev, err := database.Query("SELECT phone FROM developer WHERE legacy_key = ? and status = 1 and access_scope like ? LIMIT 1", authHeader, "%whatsapp%")
// 			if utils.CheckErr(err) || len(dev) == 0 {
// 				log.Info("this auth is is'n valid : %v | data : %v", authHeader, dev)
// 				ctx.SetStatusCode(fasthttp.StatusForbidden)
// 				return
// 			}
// 			phone = utils.ToString(dev["phone"])
// 		} else {
// 			fmt.Println("Sorry, you are not allowed to send message")
// 			ctx.SetStatusCode(fasthttp.StatusForbidden)
// 			return
// 		}
// 	}

// 	phone = strings.Replace(phone, "\u202c", "", -1)
// 	if strings.HasPrefix(phone, "+") {
// 		phone = phone[1:]
// 	}

// 	log.Info("sending to '%s' | len %d | quoted id : '%s' | chat : %s | from IP : %s", phone, len(phone), quotedId, msg, ipAddress)
// 	if !utils.IsValidPhoneNumber(phone) {
// 		ctx.SetStatusCode(fasthttp.StatusBadRequest)
// 		log.Info("User sending non phone number : %s", phone, len(phone))
// 		return
// 	}

// 	msg = strings.TrimSpace(msg)

// 	id := ""
// 	var err error
// 	status := true
// 	statusMsg := "Success"
// 	code := 0
// 	data := make(map[string]interface{})
// 	if msg != "" && msg != " " {
// 		id, err = core.SendWhatsApp(phone, msg, quotedId)
// 		if err != nil {
// 			status = false
// 			statusMsg = fmt.Sprintf("Sending message failed. Reason : %v", err)
// 			if errWithCode, ok := err.(exceptions.ErrWithCode); ok {
// 				code = errWithCode.Code
// 			} else if invalidSession, ok := err.(exceptions.InvalidSession); ok {
// 				data["restart_token"] = invalidSession.RestartToken
// 			}
// 		}
// 	} else {
// 		status = false
// 		statusMsg = "you can not send empty message!"
// 	}

// 	data["message_id"] = id
// 	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: status, Code: code, Message: statusMsg, Data: data})
// }

// func savePendingMessage(messager models.Messager) {
// 	//db := database.GetDbJson()
// 	//id :=  fmt.Sprintf("%v", time.Now().UnixNano())
// 	//messager.Id = id
// 	//err := db.Write("wa_pending", id, messager)
// 	//utils.CheckErr(err)
// 	//fmt.Println("save pending message... ", id)
// }

// //you have to run in goroutine
// func SchedulePendingMessage() {

// }

// func sendPendingMessages() (int, error) {
// 	db := database.GetDbJson()
// 	records, err := db.ReadAll("wa_pending")
// 	if utils.CheckErr(err) {
// 		return -1, err
// 	}

// 	fmt.Printf("\n-->Found %d pending messages... \n", len(records))

// 	errorCount := 0
// 	for _, f := range records {
// 		data := models.Messager{}
// 		err := json.Unmarshal([]byte(f), &data)
// 		if !utils.CheckErr(err) {
// 			if data.Type == "media" {
// 				media := map[string]string{
// 					"phone":     data.Receiver,
// 					"caption":   data.Message,
// 					"file_path": data.Resource,
// 					"file_url":  data.Media.FileUrl,
// 					"file_type": data.Media.FileType,
// 				}
// 				err = core.SendWhatsAppMedia(media)
// 			} else {
// 				_, err = core.SendWhatsApp(data.Receiver, data.Message, "")
// 			}
// 			if utils.CheckErr(err) {
// 				errorCount += 1
// 			} else {
// 				//if success, delete data
// 				err := db.Delete("wa_pending", data.Id)
// 				utils.CheckErr(err)
// 			}
// 		}
// 	}

// 	return errorCount, nil
// }

// func PostMedia(ctx *fasthttp.RequestCtx) {
// 	//ipAddress := utils.FromRequest(ctx)
// 	//if ipAddress != "127.0.0.1" {
// 	//	localIP := utils.GetOutboundIP()
// 	//	fmt.Println("IP Address Client : ", ipAddress)
// 	//	fmt.Println("Your Local IP : ", localIP.String())
// 	//	fmt.Println("Sorry, you are not allowed to send message")
// 	//	ctx.SetStatusCode(fasthttp.StatusForbidden)
// 	//	return
// 	//}

// 	phone := string(ctx.FormValue("phone"))
// 	caption := string(ctx.FormValue("caption"))
// 	filePath := string(ctx.FormValue("file_path")) //deprecated
// 	fileUrl := string(ctx.FormValue("file_url"))   //deprecated
// 	file := string(ctx.FormValue("file"))          // <-- use this instead
// 	fileType := string(ctx.FormValue("file_type")) //can be : url, base64
// 	message := string(ctx.FormValue("message"))

// 	if caption == "" && message != "" {
// 		caption = message
// 	}

// 	log.Info("file type : %s", fileType)
// 	log.Info("file: %s", file)
// 	log.Info("file url (deprecated): %s", fileUrl)

// 	isDownloadFile := false
// 	if fileType == "url" {
// 		isDownloadFile = true
// 		filePath = fmt.Sprintf("%d-%s.jpg", time.Now().UnixNano(), phone)
// 		if fileUrl == "" && file != "" {
// 			fileUrl = file
// 		}
// 		err := utils.DownloadFile(filePath, fileUrl)
// 		log.IfError(err)
// 	} else if fileType == "base64" {
// 		isDownloadFile = true
// 		filePath = fmt.Sprintf("%d-%s.jpg", time.Now().UnixNano(), phone)
// 		err := utils.DownloadFileBase64(file, filePath)
// 		log.IfError(err)
// 	}

// 	data := map[string]string{
// 		"phone":     phone,
// 		"caption":   caption,
// 		"file_path": filePath,
// 	}

// 	log.Info("sending whatsapp media : %v", data)
// 	status := true
// 	statusMsg := "Success"
// 	err := core.SendWhatsAppMedia(data)
// 	if err != nil {
// 		status = false
// 		statusMsg = fmt.Sprintf("Error - %v", err)

// 		savePendingMessage(models.Messager{Message: caption, Receiver: phone, Resource: filePath, Type: "media", Media: models.MessageMedia{
// 			FileUrl:  fileUrl,
// 			FileType: fileType,
// 		}})
// 	}

// 	if isDownloadFile {
// 		utils.DeleteFile(filePath)
// 	}

// 	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: status, Code: 0, Message: statusMsg})
// }

// //save it to firestore database
// func ReceiveTextMessage(ctx *fasthttp.RequestCtx) {
// 	chat := core.WhatsAppChat{}
// 	chat.TextMessage.Message = string(ctx.FormValue("message"))
// 	chat.Sender = string(ctx.FormValue("sender"))
// 	if string(ctx.FormValue("from")) == "me" {
// 		chat.IsFromMe = true
// 	} else {
// 		chat.IsFromMe = false
// 	}

// 	fmt.Println("receive text message from external...")
// 	core.PushMessage(chat)
// }

// func ReceiveMediaMessage(ctx *fasthttp.RequestCtx) {
// 	formValue := utils.GetFormValueMap(ctx)
// 	caption := formValue["caption"]
// 	sender := formValue["sender"]

// 	if sender == "" {
// 		fmt.Println("Sender field is required...")
// 		return
// 	}

// 	fileHeader, err := ctx.FormFile("image")
// 	if err != nil {
// 		fmt.Println("Error : ", err)
// 		return
// 	}

// 	file, err := fileHeader.Open()
// 	if err != nil {
// 		fmt.Println("fileHeader Error : ", err)
// 		return
// 	}

// 	defer file.Close()
// 	url, err := core.UploadMessageFile(file, sender, fileHeader.Filename)
// 	if utils.CheckErr(err) {
// 		fmt.Println("Upload file error ", err)
// 		return
// 	}

// 	core.SendMessage(core.WhatsAppChat{
// 		Sender:       sender,
// 		ImageMessage: core.ImageMessage{Caption: caption, ImgUrl: url},
// 	})
// }
