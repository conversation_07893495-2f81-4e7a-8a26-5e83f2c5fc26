package routers

import (
	"github.com/buaazp/fasthttprouter"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/uniq-bot/controller"
	"gitlab.com/uniqdev/backend/uniq-bot/core/auth"
)

func CreateRoutes() *fasthttprouter.Router {
	dialogFlowAuth := auth.InitWebHookAuthBackend()
	router := fasthttprouter.New()
	router.GET("/", Index)

	// router.GET("/session/login", controller.LoginOfficialWhatsApp)
	// router.GET("/session/status", controller.LoginStatus)
	// router.GET("/session/restart/:token", controller.RestartSession)

	//router.POST("/send/wa", controller.PostMessage)
	//router.POST("/send/wa/media", auth.ValidateIp(controller.PostMedia))
	router.POST("/smart_reply", dialogFlowAuth.BasicAuth(controller.WebHookHandler))

	//receiver
	// router.POST("/receive/wa/text", controller.ReceiveTextMessage)
	// router.POST("/receive/wa/media", controller.ReceiveMediaMessage)

	//WebHook
	// router.HEAD("/webhook/trello/:project", controller.WebHookTrello)
	// router.POST("/webhook/trello/:project", controller.WebHookTrello)
	router.HEAD("/webhook/trello_board", controller.WebHookTrelloNewBoard)
	router.POST("/webhook/trello_board", controller.WebHookTrelloNewBoard)

	//dynamic
	//router.GET("/whatsapp/dynamic/login/:id", auth.ValidateIp(controller.GetQrCode))
	//router.GET("/whatsapp/dynamic/login", auth.ValidateIp(controller.GetQrCode))
	// router.GET("/whatsapp/login/:id", auth.ValidateIp(controller.AddLogin))
	//router.POST("/whatsapp/dynamic/send/:id", auth.ValidateIp(controller.SendMessageDynamic))
	// router.POST("/whatsapp/dynamic/send-multiple/:id", auth.ValidateIp(controller.SendMessageDynamic))
	//router.GET("/whatsapp/dynamic/logout/:id", auth.ValidateIp(controller.LogoutFromWa))
	//router.GET("/whatsapp/dynamic/status/:id", auth.ValidateIp(controller.GetStatus))
	//router.POST("/whatsapp/dynamic/scan-status", auth.ValidateIp(controller.GetStatusByQr))
	//router.GET("/whatsapp/dynamic/scan-status", auth.ValidateIp(controller.GetStatusByQr))

	return router
}

func Index(ctx *fasthttp.RequestCtx) {
	//fmt.Println("\nIP : "+utils.GetIPAdress(ctx))
	//fmt.Println("IP Address : " + utils.FromRequest(ctx))
	ctx.Write([]byte("Hello World!"))
	ctx.SetStatusCode(fasthttp.StatusOK)
}
