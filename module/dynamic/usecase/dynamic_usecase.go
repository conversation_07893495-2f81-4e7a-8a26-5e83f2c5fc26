package usecase

import (
	"fmt"
	"net/url"
	"strings"
	"time"

	"gitlab.com/uniqdev/backend/uniq-bot/core"
	"gitlab.com/uniqdev/backend/uniq-bot/core/log"
	"gitlab.com/uniqdev/backend/uniq-bot/core/util/generate"
	"gitlab.com/uniqdev/backend/uniq-bot/domain"
	utils "gitlab.com/uniqdev/backend/uniq-bot/util"
)

var qrLogin = make(map[string]map[string]interface{})

type dynamicUseCase struct {
	service domain.WhatsAppService
}

func NewDynamicUseCase(service domain.WhatsAppService) domain.DynamicUseCase {
	service.SetDeviceName("UNIQ Social Connect")
	return dynamicUseCase{service: service}
}

func (d dynamicUseCase) Login(id string) (map[string]interface{}, error) {
	qrChan := make(chan string)
	loginStatus := make(chan domain.LoginStatus)
	go d.service.Login(id, qrChan, loginStatus)

	qrCode := <-qr<PERSON>han
	qrLogin[qrCode] = map[string]interface{}{
		"start":  time.Now().Unix() * 1000,
		"status": "waiting",
	}

	go func(qrCode string, loginStatus chan domain.LoginStatus) {
		result := <-loginStatus
		fmt.Println("===> END: ", result)
		qrLogin[qrCode]["status"] = result.Status
		qrLogin[qrCode]["phone"] = result.Phone
		qrLogin[qrCode]["error"] = result.Error
	}(qrCode, loginStatus)

	params := url.Values{}
	params.Add("cht", "qr")
	params.Add("chs", "250x250")
	params.Add("chl", qrCode)

	baseUrl, err := url.Parse("https://chart.googleapis.com")
	log.IfError(err)
	baseUrl.Path = "chart"
	baseUrl.RawQuery = params.Encode()

	result := map[string]interface{}{
		"expired":       time.Now().Add(30*time.Second).Unix() * 1000,
		"qr_code":       qrCode,
		"qr_code_image": generate.QrImageUrl(qrCode, 250),
	}

	log.Info("result: %v", result)
	//addQrLoginStatus(qrCode)
	return result, nil
}

func (d dynamicUseCase) CheckStatus(qr string) map[string]interface{} {
	return qrLogin[qr]
}

func (d dynamicUseCase) CheckLoginStatus(id string) (bool, error) {
	_, err := d.service.GetSession(id)
	if err != nil {
		return false, domain.SessionException{Code: domain.CodeNotLogIn}
	}

	return true, nil
}

func (d dynamicUseCase) Logout(id string) error {
	return d.service.Logout(id)
}

func (d dynamicUseCase) SendMessage(id string, phone string, message string, attachment domain.MessageAttachment) error {
	log.Info("try to send to %s, message: %s", phone, message)
	log.Info("attachment: %v", attachment)
	if strings.TrimSpace(phone) == "" || strings.TrimSpace(message) == "" {
		return fmt.Errorf("can not send empty phone or message")
	}

	service, err := d.service.GetSession(id)
	if err != nil {
		return domain.ResponseError{Code: 101, Message: err.Error()}
	}

	attachmentPath := ""
	if attachment.Attachment != "" {
		//handle file url
		if attachment.AttachmentType == "url" {
			attachmentPath = fmt.Sprintf(core.WaDocTmpPath, time.Now().UnixNano(), "jpg")
			log.IfError(utils.DownloadFile(attachmentPath, attachment.Attachment))
		} else if attachment.AttachmentType == "base64" {
			attachmentPath = fmt.Sprintf(core.WaDocTmpPath, time.Now().UnixNano(), "jpg")
			log.IfError(utils.DownloadFileBase64(attachment.Attachment, attachmentPath))
		} else {
			log.Info("attachment type is not support")
		}

		log.Info("attachment path: %v | exist: %v", attachmentPath, utils.IsFileExist(attachmentPath))
	}

	phone = utils.FormatWhatsAppNumber(phone)
	_, err = service.SendWhatsAppMessage(domain.WhatsApp{
		Phone:          phone,
		Message:        message,
		AttachmentPath: attachmentPath,
	})
	return err
}
