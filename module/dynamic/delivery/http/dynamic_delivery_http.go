package http

import (
	"encoding/json"
	"fmt"

	"github.com/buaazp/fasthttprouter"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/uniq-bot/core/auth"
	"gitlab.com/uniqdev/backend/uniq-bot/core/log"
	"gitlab.com/uniqdev/backend/uniq-bot/domain"
	"gitlab.com/uniqdev/backend/uniq-bot/models"
	utils "gitlab.com/uniqdev/backend/uniq-bot/util"
)

type dynamicHandler struct {
	uc domain.DynamicUseCase
}

func NewHttpDynamicRouter(app *fasthttprouter.Router, useCase domain.DynamicUseCase) {
	handler := dynamicHandler{uc: useCase}

	//session or auth
	app.GET("/whatsapp/dynamic/login", auth.ValidateIp(handler.Login))
	app.GET("/whatsapp/dynamic/login/:id", auth.ValidateIp(handler.Login))
	app.GET("/whatsapp/dynamic/logout/:id", auth.ValidateIp(handler.Logout))

	app.GET("/whatsapp/dynamic/scan-status", auth.ValidateIp(handler.CheckStatus))
	app.POST("/whatsapp/dynamic/scan-status", auth.ValidateIp(handler.CheckStatus))
	app.GET("/whatsapp/dynamic/status/:id", auth.ValidateIp(handler.CheckLoginStatus))

	//sending message
	app.POST("/whatsapp/dynamic/send/:id", auth.ValidateIp(handler.SendMessage))
}

func (h dynamicHandler) Login(ctx *fasthttp.RequestCtx) {
	id := utils.ToString(ctx.UserValue("id"))
	result, err := h.uc.Login(id)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
}

func (h dynamicHandler) Logout(ctx *fasthttp.RequestCtx) {
	id := utils.ToString(ctx.UserValue("id"))
	err := h.uc.Logout(id)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}

func (h dynamicHandler) SendMessage(ctx *fasthttp.RequestCtx) {
	id := utils.ToString(ctx.UserValue("id"))
	phone := string(ctx.PostArgs().Peek("phone"))
	message := string(ctx.PostArgs().Peek("message"))

	msgAttachment := domain.MessageAttachment{
		Attachment:     string(ctx.PostArgs().Peek("file")),
		AttachmentType: string(ctx.PostArgs().Peek("file_type")),
	}

	ctx.PostArgs().VisitAll(func(key, value []byte) {
		fmt.Printf("[FORM] '%v' : %v\n", string(key), string(value))
	})

	err := h.uc.SendMessage(id, phone, message, msgAttachment)
	if err != nil {
		response := models.ApiResponse{Status: false, Message: err.Error()}
		if respErr, ok := err.(domain.ResponseError); ok {
			response.Code = respErr.Code
			response.Message = respErr.Message
		}
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(response)
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success"})
}

func (h dynamicHandler) CheckStatus(ctx *fasthttp.RequestCtx) {
	qr := ctx.QueryArgs().Peek("qr")
	if qr == nil {
		qr = ctx.PostArgs().Peek("qr")
	}
	if qr == nil {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		log.Warn("GetStatusByQr : Qr Is Empty")
		return
	}
	result := h.uc.CheckStatus(string(qr))
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Data: result})
}

func (h dynamicHandler) CheckLoginStatus(ctx *fasthttp.RequestCtx) {
	id := utils.ToString(ctx.UserValue("id"))
	result, err := h.uc.CheckLoginStatus(id)
	if err != nil {
		if errCode, ok := err.(domain.SessionException); ok {
			ctx.SetStatusCode(errCode.Code)
		} else {
			ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		}
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: result, Data: result})
}
