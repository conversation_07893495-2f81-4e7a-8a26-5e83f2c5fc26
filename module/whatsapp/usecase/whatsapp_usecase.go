package usecase

import (
	"strings"

	"gitlab.com/uniqdev/backend/uniq-bot/core/exception"
	"gitlab.com/uniqdev/backend/uniq-bot/core/extract"
	"gitlab.com/uniqdev/backend/uniq-bot/core/log"
	"gitlab.com/uniqdev/backend/uniq-bot/domain"
	utils "gitlab.com/uniqdev/backend/uniq-bot/util"
)

const (
	WA_OFFICIAL = "6281717172171"
)

var chatLinkCache map[string]string

type whatsAppUseCase struct {
	service domain.WhatsAppService
}

func NewWhatsAppUseCase(service domain.WhatsAppService) domain.WhatsAppUseCase {
	service.SetDeviceName("UNIQ Support")
	return whatsAppUseCase{service: service}
}

func (w whatsAppUseCase) SendMessage(phone, message, attachmentPath, quotedId string) (interface{}, error) {
	if strings.TrimSpace(message) == "" && strings.TrimSpace(attachmentPath) == "" {
		log.Info("skip... empty msg")
		return nil, exception.Request{Code: 401, Message: "message is empty"}
	}

	phone = utils.FormatWhatsAppNumber(phone)
	log.Info("sending to '%s' | len %d | quoted id : '%s' | chat : %s | attachment: '%v'", phone, len(phone), quotedId, message, attachmentPath)
	if !utils.IsValidPhoneNumber(phone) {
		log.Info("User sending non phone number : %s", phone, len(phone))
		return nil, exception.Request{Code: 151, Message: "invalid phone number"}
	}

	resp, err := w.service.SendWhatsAppMessage(domain.WhatsApp{
		Phone:          phone,
		Message:        message,
		QuotedId:       quotedId,
		AttachmentPath: attachmentPath,
	})
	if err != nil {
		return nil, err
	}

	go cacheChat(phone, message)

	return map[string]interface{}{
		"message":    "message sent successfully",
		"message_id": resp.MessageId,
	}, nil
}

func cacheChat(phone, text string) {
	if !strings.Contains(text, "NOTA PEMBELIAN") {
		return
	}

	url := strings.TrimRight(extract.Link(text), "_")
	log.Info("url: ", url, " | phone: ", phone)
	if url == "" {
		return
	}

	// if len(chatLinkCache) >= 20 {
	// 	chatLinkCache = make(map[string]string)
	// }
	// chatLinkCache[phone] = url
}
