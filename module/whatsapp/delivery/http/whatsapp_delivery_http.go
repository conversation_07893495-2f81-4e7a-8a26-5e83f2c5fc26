package http

import (
	"encoding/json"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	"github.com/buaazp/fasthttprouter"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/uniq-bot/core/auth"
	"gitlab.com/uniqdev/backend/uniq-bot/core/exception"
	"gitlab.com/uniqdev/backend/uniq-bot/core/log"
	"gitlab.com/uniqdev/backend/uniq-bot/domain"
	"gitlab.com/uniqdev/backend/uniq-bot/models"
	utils "gitlab.com/uniqdev/backend/uniq-bot/util"
)

type whatsappHandler struct {
	uc domain.WhatsAppUseCase
}

func NewHttpWhatsappRouter(app *fasthttprouter.Router, useCase domain.WhatsAppUseCase) {
	handler := whatsappHandler{uc: useCase}

	app.POST("/send/wa", auth.ValidateIp(handler.SendMessage))
	app.POST("/send/wa/media", auth.ValidateIp(handler.SendMessage))
}

func (h whatsappHandler) SendMessage(ctx *fasthttp.RequestCtx) {
	phone := string(ctx.FormValue("phone"))
	message := string(ctx.FormValue("message"))
	quotedId := string(ctx.FormValue("quotedId"))

	caption := string(ctx.FormValue("caption"))    //deprecated, use 'message' instead
	filePath := string(ctx.FormValue("file_path")) //deprecated
	fileUrl := string(ctx.FormValue("file_url"))   //deprecated
	file := string(ctx.FormValue("file"))          // <-- use this instead
	fileType := string(ctx.FormValue("file_type")) // and specify the type, -> url,base64

	if strings.TrimSpace(caption) != "" && strings.TrimSpace(message) == "" {
		message = caption
	}

	if fileType == "url" {
		if fileUrl != "" && file == "" {
			file = fileUrl
		}
		ext := filepath.Ext(file)
		if ext == "" {
			ext = ".jpg"
		}

		filePath = fmt.Sprintf("%s_%v%s", filepath.Base(file), time.Now().Unix(), ext)
		log.Info("downloading '%v' to: %v", file, filePath)
		log.IfError(utils.DownloadFile(filePath, file))
	} else if fileType == "base64" {
		filePath = fmt.Sprintf("%d-%s.jpg", time.Now().UnixNano(), phone)
		log.IfError(utils.DownloadFileBase64(file, filePath))
	}

	result, err := h.uc.SendMessage(phone, message, filePath, quotedId)
	if err != nil {
		fmt.Println(err)
		if errRequest, ok := err.(exception.Request); ok {
			if errRequest.Code > 400 {
				ctx.SetStatusCode(errRequest.Code)
			}
			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Code: errRequest.Code, Message: errRequest.Message})
		} else {
			ctx.SetStatusCode(fasthttp.StatusInternalServerError)
			_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: false, Message: err.Error()})
		}
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "success", Data: result})
}
