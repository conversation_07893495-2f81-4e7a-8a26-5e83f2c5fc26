package gowhatsapp

import (
	"gitlab.com/uniqdev/backend/uniq-bot/core"
	"gitlab.com/uniqdev/backend/uniq-bot/core/log"
	"gitlab.com/uniqdev/backend/uniq-bot/domain"
)

type goWhatsAppUseCase struct {
}

func NewGoWhatsAppService() domain.WhatsAppService {
	return goWhatsAppUseCase{}
}

func (r goWhatsAppUseCase) SendWhatsAppMessage(whatsApp domain.WhatsApp) error {
	if whatsApp.AttachmentPath != "" {
		data := map[string]string{
			"phone":     whatsApp.Phone,
			"caption":   whatsApp.Message,
			"file_path": whatsApp.AttachmentPath,
		}
		log.Info("sending whatsapp media : %v", data)
		return core.SendWhatsAppMedia(data)
	}

	id, err := core.SendWhatsApp(whatsApp.Phone, whatsApp.Message, whatsApp.QuotedId)
	if err != nil {
		return err
	}

	log.Info("message sent, with id: %v", id)
	return nil
}

func (r goWhatsAppUseCase) Login(id string, qrChan chan string) error {
	panic("implement me")
}
