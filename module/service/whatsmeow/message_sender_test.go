package whatsmeow

import (
	"reflect"
	"testing"

	"go.mau.fi/whatsmeow"
)

func Test_getMediaType(t *testing.T) {
	type args struct {
		attachment string
	}
	tests := []struct {
		name string
		args args
		want whatsmeow.MediaType
	}{
		{"document",
			args{"tmp/invouce.pdf"},
			whatsmeow.MediaDocument,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getMediaType(tt.args.attachment); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>rf("getMediaType() = %v, want %v", got, tt.want)
			}
		})
	}
}
