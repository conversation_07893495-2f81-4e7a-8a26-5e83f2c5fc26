package whatsmeow

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"gitlab.com/uniqdev/backend/uniq-bot/core"
	"gitlab.com/uniqdev/backend/uniq-bot/core/google"
	"gitlab.com/uniqdev/backend/uniq-bot/core/log"
	utils "gitlab.com/uniqdev/backend/uniq-bot/util"
	"go.mau.fi/whatsmeow"
	"go.mau.fi/whatsmeow/types/events"
)

func eventHandler(evt interface{}, client *whatsmeow.Client) {
	ctx := context.Background()
	switch v := evt.(type) {
	case *events.Message:
		fmt.Println("received a message: ", v.Message.GetConversation())
		fmt.Println("--> ", utils.SimplyToJson(v.Message))

		//skip broadcast...

		if v.Message.ProtocolMessage != nil && v.Message.GetProtocolMessage().GetKey().GetRemoteJID() == "status@broadcast" {
			break
		}
		if v.Message.SenderKeyDistributionMessage != nil && v.Message.GetSenderKeyDistributionMessage().GetGroupID() == "status@broadcast" {
			break
		}

		phone := v.Info.Chat.User
		msg := strings.TrimSpace(v.Message.GetConversation())
		isFromMe := v.Info.IsFromMe
		msgId := v.Info.ID
		timeStamp := utils.ToInt64(v.Info.Timestamp.Unix() * 1000)
		msgQuoteId := ""

		log.Info("phone: '%s'", phone)
		if phone == "status" {
			break
		}

		log.Info("isFromMe: %v | msg: '%s' | allowed: %v", isFromMe, msg, core.IsMessageAllowedToSend(msg))
		if isFromMe && msg != "" && !core.IsMessageAllowedToSend(msg) {
			break
		}

		if v.Message.ExtendedTextMessage != nil {
			msg = v.Message.GetExtendedTextMessage().GetText()
			msgQuoteId = v.Message.GetExtendedTextMessage().GetContextInfo().GetStanzaID()
		}

		//for edit message
		if v.Message.ProtocolMessage != nil && v.Message.ProtocolMessage.EditedMessage != nil {
			msg = v.Message.GetProtocolMessage().GetEditedMessage().GetExtendedTextMessage().GetText()
			msgId = v.Message.GetProtocolMessage().GetKey().GetID()
		}

		if v.Message.DeviceSentMessage != nil {
			if v.Message.DeviceSentMessage.Message.ExtendedTextMessage != nil {
				msg = v.Message.GetDeviceSentMessage().GetMessage().GetExtendedTextMessage().GetText()
			}
			if v.Message.DeviceSentMessage.Message.ImageMessage != nil {
				v.Message.ImageMessage = v.Message.DeviceSentMessage.Message.ImageMessage
			}
		}

		if v.Message.ImageMessage != nil {
			fileDownloaded, err := client.DownloadAny(ctx, v.Message)
			if log.IfError(err) {
				break
			}

			if msg == "" {
				msg = v.Message.GetImageMessage().GetCaption()
			}

			fileName := fmt.Sprintf("%v.jpg", time.Now().UnixNano())
			log.IfError(os.WriteFile(fileName, fileDownloaded, os.ModePerm))
			core.UploadFile(core.WhatsAppChat{
				Sender:       phone,
				IsFromMe:     isFromMe,
				MessageId:    msgId,
				ImageMessage: core.ImageMessage{Caption: msg, ImgPath: fileName},
				TimeStamp:    timeStamp,
			})
		} else if v.Message.LocationMessage != nil {
			log.Info("\n>> LOCATION : %v\n", utils.SimplyToJson(v.Message.LocationMessage))
			//{"degreesLatitude":-7.79662464191482,"degreesLongitude":110.39245844448295,"name":"PDOS","address":"Jalan Ipda Tut Harsono no 47, Yogyakarta, DI Yogyakarta 55165","url":"https://foursquare.com/v/4dad04a604370a776f6663d8","jpegThumbnail":"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"}

			chat := v.Message.LocationMessage
			locationUrl := fmt.Sprintf("https://www.google.com/maps/search/?api=1&query=%v,%v", chat.GetDegreesLatitude(), chat.GetDegreesLongitude())
			msgText := fmt.Sprintf("<< LOCATION >>\n\n%s\n\n%s%s", locationUrl, strings.ToUpper(chat.GetName()), chat.GetAddress())

			filename := fmt.Sprintf(core.WaDocTmpPath, msgId, "jpg")
			err := utils.DownloadFileBase64(chat.GetJPEGThumbnail(), filename)

			if err == nil {
				core.UploadFile(core.WhatsAppChat{
					Sender:       phone,
					IsFromMe:     isFromMe,
					MessageId:    msgId,
					ImageMessage: core.ImageMessage{Caption: strings.TrimSpace(msgText), ImgPath: filename},
					TimeStamp:    timeStamp,
				})
			} else {
				core.PushMessage(core.WhatsAppChat{
					Sender:          phone,
					TextMessage:     core.TextMessage{Message: strings.TrimSpace(msgText)},
					IsFromMe:        isFromMe,
					MessageId:       msgId,
					TimeStamp:       timeStamp,
					MessageQuotedId: msgQuoteId,
				})
			}
		} else if v.Message.ContactMessage != nil {
			log.Info(">> CONTACT : %v\n", utils.SimplyToJson(v.Message.ContactMessage))
			// {"displayName":"Aan (choirul Anwar) - Amikom","vcard":"BEGIN:VCARD\nVERSION:3.0\nN:Amikom;Aan (choirul Anwar);-;;\nFN:Aan (choirul Anwar) - Amikom\nitem1.TEL;waid=6285727333946:+62 857-2733-3946\nitem1.X-ABLabel:Mobile\nEMAIL;TYPE=Work:v \nPHOTO;BASE64: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\nEND:VCARD"}
			vcard := utils.ExtractVCard(v.Message.ContactMessage.GetVcard())
			contactInfo := fmt.Sprintf(
				"Name  : %s \n"+
					"Phone : %s", v.Message.ContactMessage.GetDisplayName(), strings.Join(vcard.Phones, ",\n"))

			core.PushMessage(core.WhatsAppChat{
				Sender:      phone,
				TextMessage: core.TextMessage{Message: "*<< _CONTACT MESSAGE_ >>*\n\n" + contactInfo},
				IsFromMe:    isFromMe,
				MessageId:   msgId,
				TimeStamp:   timeStamp,
			})
		} else if v.Message.DocumentMessage != nil {
			fileDownloaded, err := client.DownloadAny(ctx, v.Message)
			if log.IfError(err) {
				break
			}
			if msg == "" {
				msg = v.Message.GetDocumentMessage().GetCaption()
			}
			if msg == "" {
				msg = v.Message.GetDocumentMessage().GetFileName()
			}

			fileName := fmt.Sprintf("%v_%v", time.Now().UnixNano(), v.Message.GetDocumentMessage().GetFileName())
			fileName = strings.ReplaceAll(fileName, " ", "_")
			log.IfError(os.WriteFile(fileName, fileDownloaded, os.ModePerm))
			core.UploadFile(core.WhatsAppChat{
				Sender:          phone,
				IsFromMe:        isFromMe,
				MessageId:       msgId,
				TextMessage:     core.TextMessage{Message: msg},
				DocumentMessage: core.DocumentMessage{TempFilePath: fileName},
				TimeStamp:       timeStamp,
			})
		} else if msg != "" {
			core.PushMessage(core.WhatsAppChat{
				Sender:          phone,
				TextMessage:     core.TextMessage{Message: msg},
				IsFromMe:        isFromMe,
				MessageId:       msgId,
				TimeStamp:       timeStamp,
				MessageQuotedId: msgQuoteId,
				Helper:          helper{client: client},
			})
			go publishMessage(msg, phone)
		}
	case *events.Receipt:
		fmt.Println("--> received a receipt:", v.Type.GoString())
		fmt.Println("--> receipt:", utils.SimplyToJson(v))
		if v.Type.GoString() == events.ReceiptTypeRead.GoString() && !v.IsFromMe {
			fmt.Printf("msg read by %v, ids: %v", v.Chat.User, v.MessageIDs)
			for _, id := range v.MessageIDs {
				core.UpdateChatStatus(v.Chat.User, id, "read")
			}
		}
	default:
		fmt.Println("->> unhandled event:", utils.SimplyToJson(v))
	}

}

func publishMessage(message, sender string) {
	// Publish the message to a message queue or any other service
	fmt.Println("Publishing message:", message)
	google.PublishMessage(map[string]interface{}{
		"message":   message,
		"timestamp": time.Now().Unix(),
		"type":      "whatsapp",
		"sender":    sender,
	}, "incoming_message")
}
