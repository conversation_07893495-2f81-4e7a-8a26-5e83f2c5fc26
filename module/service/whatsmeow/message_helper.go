package whatsmeow

import (
	"fmt"
	"io/ioutil"
	"net/http"
	"regexp"
	"strings"

	"go.mau.fi/whatsmeow"
	"go.mau.fi/whatsmeow/types"
)

type helper struct {
	client *whatsmeow.Client
}

func (h helper) FetchProfile(phone string) string {
	jid, err := types.ParseJID(fmt.Sprintf("%s%s", phone, "@s.whatsapp.net"))
	if err != nil {
		fmt.Println("fetching jid err: , err")
		return ""
	}

	profile, err := h.client.GetProfilePictureInfo(jid, nil)
	if err != nil {
		fmt.Println("fetching profile err: ", err)
		return ""
	}

	return profile.URL
}

func getMediaType(attachment string) whatsmeow.MediaType {
	mediType := whatsmeow.MediaImage

	mediaList := map[whatsmeow.MediaType]string{
		whatsmeow.MediaImage:    "jpg,png,gif,webp,jpeg",
		whatsmeow.MediaDocument: "doc,pdf,docx,xls,xlsx,ppt,pptx",
	}

	for media, exts := range mediaList {
		for _, ext := range strings.Split(exts, ",") {
			if strings.HasSuffix(attachment, ext) {
				return media
			}
		}
	}

	return mediType
}

// return application/pdf, text/html
func readFileContentType(filename string) string {
	fileByte, err := ioutil.ReadFile(filename)
	if err != nil {
		fmt.Println(err)
		return ""
	}

	contentType := http.DetectContentType(fileByte)
	p := `([a-z]+/[a-z]+)`
	contentType = regexp.MustCompile(p).FindString(contentType)
	fmt.Printf("content type of '%s' : '%s'\n", filename, contentType)
	return contentType
}
