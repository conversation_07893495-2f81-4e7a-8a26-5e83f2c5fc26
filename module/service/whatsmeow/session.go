package whatsmeow

import (
	"context"
	"fmt"
	"os"

	_ "github.com/mattn/go-sqlite3"
	"github.com/mdp/qrterminal"
	"gitlab.com/uniqdev/backend/uniq-bot/core"
	"gitlab.com/uniqdev/backend/uniq-bot/core/log"
	"gitlab.com/uniqdev/backend/uniq-bot/domain"
	utils "gitlab.com/uniqdev/backend/uniq-bot/util"
	"go.mau.fi/whatsmeow"
	"go.mau.fi/whatsmeow/store"
	"go.mau.fi/whatsmeow/store/sqlstore"
	waLog "go.mau.fi/whatsmeow/util/log"
)

type WhatsAppSession struct {
	client *whatsmeow.Client
}

func logLevel() string {
	if os.Getenv("wa_type") == "dynamic" {
		return "ERROR"
	}
	return "DEBUG"
}

func getContainer() (*sqlstore.Container, error) {
	dbName := "official_session.db"
	if os.Getenv("wa_type") == "dynamic" {
		dbName = "dynamic_session.db"
	}

	dbLog := waLog.Stdout("Database", logLevel(), true)
	container, err := sqlstore.New(context.Background(), "sqlite3", fmt.Sprintf("file:temp/sessions/%s?_foreign_keys=on", dbName), dbLog)

	if !log.IfError(err) {
		log.Info("### session db created, %v", dbName)
	}
	return container, nil
}

func (s whatsMeowService) initSession() WhatsAppSession {
	ctx := context.Background()
	devices, err := s.container.GetAllDevices(ctx)
	log.Info("total devices: %d", len(devices))
	if err != nil {
		fmt.Println("err get all devices: ", err)
	}

	var deviceStore *store.Device
	if len(devices) > 0 {
		fmt.Println("--> device 0 : ", utils.SimplyToJson(devices[0]))
		fmt.Println("--> jid 0 : ", utils.SimplyToJson(devices[0].ID))
		fmt.Println("--> user 0 : ", utils.SimplyToJson(devices[0].ID.User))
		deviceStore, err = s.container.GetDevice(ctx, *devices[0].ID)
		if err != nil {
			panic(err)
		}
	} else {
		// If you want multiple sessions, remember their JIDs and use .GetDevice(jid) or .GetAllDevices() instead.
		deviceStore, err = s.container.GetFirstDevice(ctx)
		if err != nil {
			panic(err)
		}
	}

	clientLog := waLog.Stdout("Client", logLevel(), true)
	client := whatsmeow.NewClient(deviceStore, clientLog)
	client.AddEventHandler(func(evt interface{}) {
		eventHandler(evt, client)
	})

	log.Info("client store id: %v", client.Store.ID)
	if client.Store.ID == nil {
		// No ID stored, new login
		qrChan, _ := client.GetQRChannel(context.Background())
		err = client.Connect()
		if err != nil {
			panic(err)
		}
		for evt := range qrChan {
			if evt.Event == "code" {
				// Render the QR code here
				// e.g. qrterminal.GenerateHalfBlock(evt.Code, qrterminal.L, os.Stdout)
				// or just manually `echo 2@... | qrencode -t ansiutf8` in a terminal
				qrterminal.GenerateHalfBlock(evt.Code, qrterminal.L, os.Stdout)
				fmt.Println("QR code:", evt.Code)
				fileName, err := utils.GenerateQrFile(evt.Code)
				if err == nil {
					core.SendFileToSlack(fileName)
				}
			} else {
				fmt.Println("Login event:", evt.Event)
			}
		}
	} else {
		// Already logged in, just connect
		err = client.Connect()
		if err != nil {
			panic(err)
		}

		// client.IsConnected()
	}

	return WhatsAppSession{client: client}
}

func initDynamicSession(container *sqlstore.Container, id string, qr chan string, status chan domain.LoginStatus) error {
	// deviceStore, err := container.GetFirstDevice()
	// if log.IfError(err) {
	// 	return err
	// }

	clientLog := waLog.Stdout("Client", logLevel(), true)
	client := whatsmeow.NewClient(container.NewDevice(), clientLog)

	if client.Store.ID == nil {
		// No ID stored, new login
		qrChan, _ := client.GetQRChannel(context.Background())
		err := client.Connect()
		if log.IfError(err) {
			return err
		}
		for evt := range qrChan {
			if evt.Event == "code" {
				// Render the QR code here
				qrterminal.GenerateHalfBlock(evt.Code, qrterminal.L, os.Stdout)
				fmt.Println("QR code:", evt.Code)
				qr <- evt.Code
			} else if evt.Event == "success" {
				log.Info("login success, phone: %s, isLoggedIn: %v", client.Store.ID.User, client.IsLoggedIn())
				status <- domain.LoginStatus{
					Status: "success",
					Phone:  client.Store.ID.User,
				}
			} else {
				fmt.Println("------> Login event:", evt.Event)
				fmt.Println(evt)
			}
		}
	}
	return nil
}

func getSession(container *sqlstore.Container, id string) (WhatsAppSession, error) {
	if container == nil {
		log.IfError(fmt.Errorf("container not has been initialized"))
		return WhatsAppSession{}, fmt.Errorf("container not has been initialized")
	}

	ctx := context.Background()
	session := WhatsAppSession{}
	devices, err := container.GetAllDevices(ctx)
	log.IfError(err)
	log.Info("total devices: %d", len(devices))

	if len(devices) == 0 {
		return session, fmt.Errorf("user %s not loged in yet", id)
	}

	log.Info("first jid: user: %v, server: %v, ad: %v", devices[0].ID.User, devices[0].ID.Server, devices[0].ID)

	var deviceStore *store.Device
	allDevices, err := container.GetAllDevices(ctx)
	if log.IfError(err) {
		return session, err
	}

	for _, device := range allDevices {
		if device.ID.User == id {
			deviceStore = device
		}
	}

	log.Info("err: %v | device-store: %v", err, deviceStore)
	if deviceStore == nil {
		return session, fmt.Errorf("user %s not loged in yet", id)
	}

	clientLog := waLog.Stdout("Client", "DEBUG", true)
	client := whatsmeow.NewClient(deviceStore, clientLog)
	log.Info("client store id: %v", client.Store.ID)

	if client.Store.ID == nil {
		log.Info("not found")
	} else {
		err = client.Connect()
		log.IfError(err)
	}

	fmt.Println("get session success...", deviceStore.PushName)
	session.client = client
	return session, err
}
