package firebase

import (
	"context"
	"firebase.google.com/go"
	"fmt"
	"google.golang.org/api/option"
	"os"
)

var firebaseApp *firebase.App

func init() {
	var err error
	fmt.Println("[START initialize_app_service_account_golang]")

	config := &firebase.Config{
		StorageBucket: "chat-support-102fc.appspot.com",
	}
	opt := option.WithCredentialsFile("config/auth/chat-support-102fc-firebase-adminsdk.json")
	firebaseApp, err = firebase.NewApp(context.Background(), config, opt)
	if err != nil {
		fmt.Println("[WARN] initialize firebase error: ", err)
		if os.Getenv("wa_type") == "official" {
			fmt.Println("[PANIC] wa official should have firebase sdk credential")
			panic(err)
		}
	}
}

func GetFirebaseApp() *firebase.App {
	return firebaseApp
}
