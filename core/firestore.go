package core

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"time"

	"cloud.google.com/go/firestore"
	"cloud.google.com/go/storage"
	"gitlab.com/uniqdev/backend/uniq-bot/core/firebase"
	log2 "gitlab.com/uniqdev/backend/uniq-bot/core/log"
	utils "gitlab.com/uniqdev/backend/uniq-bot/util"
	"google.golang.org/api/iterator"
)

type WhatsAppHelper interface {
	FetchProfile(string) string
	// FetchGroupInfo(string) string
}

type WhatsAppChat struct {
	Sender          string
	IsFromMe        bool
	TextMessage     TextMessage
	ImageMessage    ImageMessage
	DocumentMessage DocumentMessage
	VideoMessage    VideoMessage
	MessageId       string
	MessageQuotedId string
	Username        string
	TimeStamp       int64
	Helper          WhatsAppHelper
}

type TextMessage struct {
	Message string
}

type ImageMessage struct {
	Caption string
	ImgPath string
	ImgUrl  string
	ImgFile os.File
}

type DocumentMessage struct {
	TempFilePath string
	FileUrl      string
}

type VideoMessage struct {
	TempFilePath      string
	TempThumbnailPath string
	FileUrl           string
	ThumbnailUrl      string
}

const (
	Text     = "text"
	Image    = "image"
	Video    = "video"
	Document = "document"
)

func PushMessage(chat WhatsAppChat) {
	ctx := context.Background()
	app := firebase.GetFirebaseApp()
	client, err := app.Firestore(context.Background())
	if err != nil {
		fmt.Println("Firestore client error - ", err)
	}
	defer client.Close()

	dsnap, err := client.Collection("contact").Doc(chat.Sender).Get(ctx)
	if err != nil {
		fmt.Printf("Failed read data: %v \n", err)
	}
	m := dsnap.Data()

	isPhotoExpired := false
	if len(m) > 0 {
		created := utils.ToInt64(m["timeModified"])
		isPhotoExpired = (time.Unix(created, 0).Sub(time.Now()).Hours() / 24) > 7
		chat.Username = utils.ToString(m["name"])
	}

	//Insert new phone number
	if len(m) == 0 || len(utils.ToString(m["photo"])) <= 0 || isPhotoExpired {
		// profile, _ := GetPhotoProfile(chat.Sender)
		profile := ""
		if chat.Helper != nil {
			profile = chat.Helper.FetchProfile(chat.Sender)
			fmt.Println("profile pict: ", profile)
		} else {
			fmt.Println("helper not provided...")
		}

		contact := map[string]interface{}{
			"timeModified": time.Now().Unix() * 1000,
		}

		if len(m) == 0 {
			contact["timeCreated"] = time.Now().Unix() * 1000
			contact["phone"] = chat.Sender
		}
		if profile != "" {
			contact["photo"] = profile
		}

		_, err = client.Collection("contact").Doc(chat.Sender).Set(ctx, contact, firestore.MergeAll)
		if err != nil {
			log.Fatalf("Failed adding phone number: %v", err)
		}
	}

	//get unreadCount
	unreadCount := 0
	if !chat.IsFromMe && len(m) > 0 {
		dsnap, err = client.Collection("chat").Doc(chat.Sender).Get(ctx)
		if err != nil {
			fmt.Printf("Failed read data chat.  - Error: %v \n", err)
		}
		data := dsnap.Data()

		if len(data) > 0 {
			unreadCount = utils.ToInt(data["unreadCount"])
		}
	}

	//update last chat
	_, err = client.Collection("chat").Doc(chat.Sender).Set(ctx, map[string]interface{}{
		"lastChatTime":    time.Now().Unix() * 1000,
		"lastChatMessage": chat.TextMessage.Message,
		"phone":           chat.Sender,
		"unreadCount":     unreadCount + 1,
	}, firestore.MergeAll)
	if err != nil {
		log.Fatalf("Failed to update last chat: %v", err)
	}

	//add message
	userType := "customer"
	if chat.IsFromMe {
		userType = "admin"
	}

	chatMap := map[string]interface{}{
		"timeMillis": chat.TimeStamp,
		"message":    chat.TextMessage.Message,
		"sender":     chat.Sender,
		"userType":   userType,
		"status":     "sent",
		"messageId":  chat.MessageId,
	}

	if chat.MessageQuotedId != "" {
		chatMap["messageQuotedId"] = chat.MessageQuotedId
		iter := client.Collection("chat").Doc(chat.Sender).Collection("messages").Where("messageId", "==", chat.MessageQuotedId).Documents(ctx)
		for {
			doc, err := iter.Next()
			if err == iterator.Done {
				break
			}
			if err != nil {
				break
			}
			chatMap["messageQuoted"] = doc.Data()["message"]
		}
	}

	messageRef := client.Collection("chat").Doc(chat.Sender).Collection("messages")
	if chat.MessageId != "" {
		log2.Info("chat added to: %v", chat.MessageId)
		_, err = messageRef.Doc(chat.MessageId).Set(context.Background(), chatMap)
		log2.IfError(err)
	} else {
		ref, _, err := messageRef.Add(context.Background(), chatMap)
		if !log2.IfError(err) {
			log2.Info("chat added to new id: %v", ref.ID)
		}
	}

	//send notification to customer support
	if !chat.IsFromMe {
		sendChatNotification(chat)
	}
}

func UploadMessageFile(file multipart.File, sender, filename string) (string, error) {
	ctx := context.Background()
	client, err := firebase.GetFirebaseApp().Storage(ctx)
	if err != nil {
		fmt.Println("Firebase Storage error - ", err)
	}

	bucket, err := client.DefaultBucket()
	if err != nil {
		fmt.Println("create bucker instance error - ", err)
		return "", err
	}

	storageDir := fmt.Sprintf("chat-image/%s/%s", sender, filename)
	wc := bucket.Object(storageDir).NewWriter(ctx)
	if _, err := io.Copy(wc, file); err != nil {
		fmt.Println("Doing copy file error - ", err)
		return "", err
	}

	if err := wc.Close(); err != nil {
		fmt.Println("Uploading error - ", err)
		return "", err
	}

	pkey, err := ioutil.ReadFile("config/auth/google_signedurl_key.pem")
	if err != nil {
		fmt.Println("read pkey err ", err)
		return "", err
	}

	url, err := storage.SignedURL("chat-support-102fc.appspot.com", storageDir, &storage.SignedURLOptions{
		GoogleAccessID: "<EMAIL>",
		PrivateKey:     pkey,
		Method:         "GET",
		Expires:        time.Now().AddDate(0, 1, 0),
	})

	if err != nil {
		fmt.Println("Get url err - ", err)
	}

	fmt.Println("Upload image done")

	return url, nil
}

func UploadFile(chat WhatsAppChat) {
	ctx := context.Background()
	client, err := firebase.GetFirebaseApp().Storage(ctx)
	if err != nil {
		fmt.Println("Firebase Storage error - ", err)
	}

	bucket, err := client.DefaultBucket()
	if err != nil {
		fmt.Println("create bucket instance error - ", err)
		return
	}

	filePath := chat.ImageMessage.ImgPath
	if chat.ChatType() == Document {
		filePath = chat.DocumentMessage.TempFilePath
	} else if chat.ChatType() == Video {
		filePath = chat.VideoMessage.TempFilePath
	}

	f, err := os.Open(filePath)
	if log2.IfError(err) {
		log2.Info("this file not found : %s", filePath)
		return
	}
	defer f.Close()

	storageDir := fmt.Sprintf("chat-%s/%s/%s", chat.ChatType(), chat.Sender, filepath.Base(filePath))
	obj := bucket.Object(storageDir)
	wc := obj.NewWriter(ctx)
	if _, err := io.Copy(wc, f); err != nil {
		fmt.Println("Doing copy file error - ", err)
		return
	}

	if err := wc.Close(); err != nil {
		fmt.Println("Uploading error - ", err)
		return
	}

	if err := obj.ACL().Set(ctx, storage.AllUsers, storage.RoleReader); err != nil {
		fmt.Println(err)
		return
	}

	attrs, err := obj.Attrs(ctx)
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Printf("URL --> https://storage.googleapis.com/%s/%s\n", attrs.Bucket, attrs.Name)
	url := fmt.Sprintf("https://storage.googleapis.com/%s/%s", attrs.Bucket, attrs.Name)

	//pkey, err := ioutil.ReadFile("config/auth/google_signedurl_key.pem")
	//if err != nil {
	//	fmt.Println("read pkey err ", err)
	//	return
	//}
	//
	//url, err := storage.SignedURL("chat-support-102fc.appspot.com", storageDir, &storage.SignedURLOptions{
	//	GoogleAccessID: "<EMAIL>",
	//	PrivateKey:     pkey,
	//	Method:         "GET",
	//	Expires:        time.Now().AddDate(1, 0, 0),
	//})
	//
	//if err != nil {
	//	fmt.Println("Get url err - ", err)
	//}

	//delete file from temp
	err = os.Remove(filePath)
	log2.IfError(err)

	fmt.Println("Upload file done")
	if chat.ChatType() == Image {
		chat.ImageMessage.ImgUrl = url
	} else if chat.ChatType() == Document {
		chat.DocumentMessage.FileUrl = url
		chat.TextMessage.Message = chat.TextMessage.Message + "\n\n" + url
	} else if chat.ChatType() == Video {
		chat.VideoMessage.FileUrl = url
		chat.TextMessage.Message = chat.TextMessage.Message + "\n\n" + url
	}

	SendMessage(chat)
	sendChatNotification(chat)
}

func SendMessage(chat WhatsAppChat) {
	db, err := firebase.GetFirebaseApp().Firestore(context.Background())
	if err != nil {
		fmt.Println("init db error - ", err)
		return
	}
	defer db.Close()

	//add message
	userType := "customer"
	if chat.IsFromMe {
		userType = "admin"
	}

	fileUrl := chat.DocumentMessage.FileUrl
	var msg, imgUrl interface{} = chat.TextMessage.Message, nil
	if chat.ChatType() == Image {
		msg = chat.ImageMessage.Caption
		imgUrl = chat.ImageMessage.ImgUrl
	} else if chat.ChatType() == Video {
		fileUrl = chat.VideoMessage.FileUrl
	}

	chatMap := map[string]interface{}{
		"timeMillis":  time.Now().Unix() * 1000,
		"message":     msg,
		"imgUrl":      imgUrl,
		"fileUrl":     fileUrl,
		"sender":      chat.Sender,
		"userType":    userType,
		"status":      "sent",
		"messageId":   chat.MessageId,
		"messageType": chat.ChatType(),
	}

	messageRef := db.Collection("chat").Doc(chat.Sender).Collection("messages")
	if chat.MessageId != "" {
		_, err = messageRef.Doc(chat.MessageId).Set(context.Background(), chatMap)
		log2.IfError(err)
	} else {
		_, _, err = messageRef.Add(context.Background(), chatMap)
		log2.IfError(err)
	}
}

func sendChatNotification(chat WhatsAppChat) {
	db, err := firebase.GetFirebaseApp().Firestore(context.Background())
	if err != nil {
		fmt.Println("init db error - ", err)
		return
	}
	defer db.Close()

	regIds := make([]string, 0)
	iter := db.Collection("customer_support").Documents(context.Background())
	for {
		doc, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			fmt.Println("error - ", err)
			return
		}
		data := doc.Data()
		if data["token"] != nil {
			switch token := data["token"].(type) {
			case []string:
				regIds = append(regIds, token...)
			case []interface{}:
				for _, arrInter := range token {
					regIds = append(regIds, utils.ToString(arrInter))
				}
			default:
				regIds = append(regIds, utils.ToString(data["token"]))
			}
		}
	}

	msg := chat.TextMessage.Message
	if chat.ChatType() == Image {
		msg = chat.ImageMessage.Caption
		if msg == "" {
			msg = "📷 Photo"
		}
	}

	if chat.Username == "" {
		chat.Username = chat.Sender
	}

	notifData := map[string]interface{}{
		"registration_ids": regIds,
		"data": map[string]interface{}{
			"message":  msg,
			"sender":   chat.Sender,
			"username": chat.Username,
		},
	}

	dataJson, err := json.Marshal(notifData)
	if err != nil {
		fmt.Println("Parse map to json error : ", err)
	}

	req, err := http.NewRequest("POST", "https://fcm.googleapis.com/fcm/send", bytes.NewBuffer(dataJson))
	req.Header.Set("Authorization", "key = AIzaSyBrIMhf4i30ZhpwkwYAVuxCDLAcK65HGO0")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if utils.CheckErr(err) {
		fmt.Println("Send notif error - ", err)
	}
	defer resp.Body.Close()

	fmt.Println("Send Notif Done. Response Status: ", resp.Status)
}

func (chat WhatsAppChat) ChatType() string {
	if chat.DocumentMessage.TempFilePath != "" {
		return Document
	} else if chat.VideoMessage.TempFilePath != "" {
		return Video
	} else if chat.TextMessage.Message != "" {
		return Text
	} else {
		return Image
	}
}

type ChatHistory struct {
	Message  string `json:"message"`
	UserType string `json:"userType"`
}

func GetChatHistory(contact, messageId string) (WhatsAppChat, error) {
	ctx := context.Background()
	client, err := firebase.GetFirebaseApp().DatabaseWithURL(ctx, "https://chat-support-102fc.firebaseio.com")
	if err != nil {
		fmt.Println("database client error - ", err)
		return WhatsAppChat{}, err
	}

	var chatHistory ChatHistory
	ref := client.NewRef("chat/" + contact + "/" + messageId)

	if err := ref.Get(ctx, &chatHistory); err != nil {
		return WhatsAppChat{}, err
	}

	waChat := WhatsAppChat{}
	waChat.TextMessage.Message = chatHistory.Message
	if chatHistory.UserType == "customer" {
		waChat.Sender = contact
	} else {
		waChat.Sender = "6281717172171"
	}
	return waChat, nil
}

func UpdateChatStatus(phone, msgId, status string) {
	ctx := context.Background()
	db, err := firebase.GetFirebaseApp().Firestore(ctx)
	if err != nil {
		fmt.Println("init db error - ", err)
		return
	}
	defer db.Close()

	docId := msgId

	//try to find doc by the id
	messageRef := db.Collection("chat").Doc(phone).Collection("messages")
	_, err = messageRef.Doc(msgId).Get(ctx)

	//if error, might be data is not found, then try to search with 'Where' query
	if err != nil {
		iter := messageRef.Where("messageId", "==", msgId).Documents(ctx)
		for {
			doc, err := iter.Next()
			if err == iterator.Done {
				break
			}
			if err != nil {
				fmt.Println(err)
			}
			//fmt.Println(doc.Data())
			fmt.Println("id", doc.Ref.ID)
			docId = doc.Ref.ID
		}
	}

	_, err = messageRef.Doc(docId).Set(ctx, map[string]interface{}{
		"status": status,
	}, firestore.MergeAll)

	if err != nil {
		fmt.Println("updating status chat err ", err)
	}
}

func UpdateTyping(customerPhone, writerName string, isTyping bool) {
	ctx := context.Background()
	client, err := firebase.GetFirebaseApp().DatabaseWithURL(ctx, "https://chat-support-102fc.firebaseio.com")
	if err != nil {
		fmt.Println("failed getting firebaseApp", err)
		return
	}

	err = client.NewRef(fmt.Sprintf("typing/%s", customerPhone)).Set(ctx, map[string]interface{}{
		writerName: time.Now().Unix() * 1000,
	})
	if err != nil {
		fmt.Println("set typing error", err)
	}
}
