package core

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	url2 "net/url"
	"strings"
)

type Simsimi struct {
	Response string `json:"response"`
	ID       int    `json:"id"`
	Result   int    `json:"result"`
	Msg      string `json:"msg"`
}

func GetSimSimiAnswer(question string) string {
	key := "f2a8162a-0355-4426-980a-09938229b2d8"
	url := fmt.Sprintf("http://sandbox.api.simsimi.com/request.p?key=%s&lc=id&ft=1.0&text=%s", key, url2.QueryEscape(question))
	fmt.Println("Url ", url)

	resp, err := http.Get(url)
	if err != nil {
		fmt.Println("Simsimi request error : ", err)
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)

	simsimi := new(Simsimi)
	json.Unmarshal(body, &simsimi)

	return filterWord(simsimi.Response)
}

func filterWord(message string) string {
	r := strings.NewReplacer("simsimi", "UNIQ",
		"simi", "UNIQ")
	return r.Replace(message)
}
