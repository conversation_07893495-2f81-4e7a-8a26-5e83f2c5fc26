package core

// "github.com/Rhymen/go-whatsapp"
// "github.com/Rhymen/go-whatsapp/binary/proto"

// func SendWhatsApp(phone, messsage, quotedId string) (string, error) {
// 	if strings.HasPrefix(phone, "08") {
// 		phone = "62" + phone[1:]
// 	}

// 	//check if whatsapp is initialized and loged in
// 	if wac == nil {
// 		log.Info("a message failed to send because WhatsApp is uninitialized")
// 		return "", exceptions.ErrWithCode{
// 			Code:    50,
// 			Message: "whatsapp is uninitialized",
// 		}
// 	}

// 	//sending using waboxapp (https://www.waboxapp.com)
// 	//err := sendUsingWaBoxApp(phone, messsage)

// 	//send using code being developed internally
// 	//err := sendUsingCustomWebApp(phone, messsage)
// 	originPhone := phone
// 	if strings.Contains(phone, "-") {
// 		phone = phone + "@g.us"
// 	} else {
// 		phone = phone + "@s.whatsapp.net"
// 	}

// 	log.Info("quoted id : %s", quotedId)
// 	msg := whatsapp.TextMessage{
// 		Info: whatsapp.MessageInfo{
// 			RemoteJid: phone,
// 		},
// 		Text: messsage,
// 	}

// 	if quotedId != "" {
// 		chatHistory, err := GetChatHistory(originPhone, quotedId)
// 		if !log.IfError(err) {
// 			previousMessage := chatHistory.TextMessage.Message
// 			quotedMessage := proto.Message{
// 				Conversation: &previousMessage,
// 			}
// 			msg.ContextInfo.QuotedMessageID = quotedId
// 			msg.ContextInfo.QuotedMessage = &quotedMessage
// 			msg.ContextInfo.Participant = fmt.Sprintf("%<EMAIL>", chatHistory.Sender)
// 		}
// 	}

// 	if isMessageInTimeOutList(phone, messsage) {
// 		testPing()
// 		return "", fmt.Errorf("message is already in timeout list")
// 	}

// 	timeSend := time.Now().Unix()
// 	msgId, err := wac.Send(msg)
// 	if log.IfError(err) {
// 		fmt.Println("time send : ", timeSend)
// 		if err.Error() == "sending message timed out" {
// 			//timeout : mean message typed, but currently the mobile phone is disconnected from internet,
// 			//this message should not send again, otherwise once the phone is connected the message will be sent more than one
// 			if isMessageInTimeOutList(phone, messsage) {
// 				log.IfError(fmt.Errorf("sending message timed out reoccured"))
// 				return "", fmt.Errorf("sending message timed out, message already in list")
// 			} else {
// 				saveAsTimeOutMessage(phone, messsage)
// 			}
// 		} else if err.Error() == "could not send proto: failed to write message: invalid websocket" {
// 			generateRestartToken()
// 			log.IfError(fmt.Errorf("https://bot.uniq.id/system/wa/restart/%s", restartToken))

// 			return "", exceptions.InvalidSession{
// 				RestartToken: restartToken,
// 				Message:      err.Error(),
// 			}
// 		}
// 	} else {
// 		//if message successfully sent, its mean phone is connected,
// 		//its time to delete all timeOutMessageList
// 		//if isMessageInTimeOutList(phone, messsage) {
// 		//	setTimeOutMessageSent(phone, messsage)
// 		//}
// 		removeAllTimeOutMessage()
// 	}

// 	return msgId, err
// }

// func SendWhatsAppMedia(data map[string]string) error {
// 	//check if whatsapp is initialized and loged in
// 	if wac == nil {
// 		log.Info("a message failed to send because WhatsApp is uninitialized")
// 		return exceptions.ErrWithCode{
// 			Code:    50,
// 			Message: "whatsapp is uninitialized",
// 		}
// 	}

// 	fmt.Println(data)
// 	phone := utils.ToString(data["phone"])
// 	if strings.HasPrefix(phone, "08") {
// 		phone = "62" + phone[1:]
// 	}

// 	//sending using selenium
// 	//sendMediaUsingCustomWebApp(data)

// 	if strings.Contains(phone, "-") {
// 		phone = phone + "@g.us"
// 	} else {
// 		phone = phone + "@s.whatsapp.net"
// 	}

// 	filePath := data["file_path"]
// 	log.Info("sending media file path: %s", filePath)

// 	img, err := os.Open(filePath)
// 	if log.IfError(err) {
// 		return err
// 	}

// 	msg := whatsapp.ImageMessage{
// 		Info: whatsapp.MessageInfo{
// 			RemoteJid: phone,
// 		},
// 		Type:    "image/jpeg",
// 		Caption: utils.ToString(data["caption"]),
// 		Content: img,
// 	}

// 	_, err = wac.Send(msg)
// 	if log.IfError(err) {
// 		return err
// 	}

// 	return nil
// }
