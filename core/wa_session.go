package core

// "github.com/Rhymen/go-whatsapp"

// func RestartWhatsApp(token string) error {
// 	if os.Getenv("wa_type") != "dynamic" && isValidRestartToken(token) {
// 		restartToken = ""
// 		err := restoreSession()
// 		if err != nil {
// 			LoginOfficialAsync()
// 			return nil
// 		}
// 	}
// 	return fmt.Errorf("can not restart with wa type %s and token %s", os.Getenv("wa_type"), token)
// }

// func restoreSession() error {
// 	fmt.Println("restoring session...")
// 	err := initWAClient()
// 	if err != nil {
// 		log.IfError(fmt.Errorf("can not establish connection to wa: %v", err))
// 		return err
// 	}

// 	//try to restore session
// 	session, err := readSession()
// 	if !log.IfError(err) {
// 		//restore session
// 		session, err = wac.RestoreWithSession(session)
// 		if log.IfError(err) {
// 			log.Warn("restoring session failed: %v\n", err)
// 			wac = nil

// 			generateRestartToken()
// 			log.IfError(fmt.Errorf("https://bot.uniq.id/system/wa/restart/%s", restartToken))

// 			return err
// 		}
// 		log.Info("restoring session success")
// 		checkVersion()
// 		wac.AddHandler(&waHandler{wac})
// 		return nil
// 	} else {
// 		return err
// 	}
// }

// func readSession() (whatsapp.Session, error) {
// 	session := whatsapp.Session{}
// 	file, err := os.Open(fmt.Sprintf(WaSessionPathFormat, "official"))
// 	if err != nil {
// 		return session, err
// 	}
// 	defer file.Close()
// 	decoder := gob.NewDecoder(file)
// 	err = decoder.Decode(&session)
// 	if utils.CheckErr(err) {
// 		return session, err
// 	}
// 	return session, nil
// }

// func writeSession(session whatsapp.Session) error {
// 	file, err := os.Create(fmt.Sprintf(WaSessionPathFormat, "official"))
// 	if err != nil {
// 		return err
// 	}
// 	defer file.Close()
// 	encoder := gob.NewEncoder(file)
// 	err = encoder.Encode(session)
// 	if utils.CheckErr(err) {
// 		return err
// 	}
// 	return nil
// }
