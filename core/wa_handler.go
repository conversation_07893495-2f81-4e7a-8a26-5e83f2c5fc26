package core

import (
	"regexp"
	"strings"
)

// "github.com/Rhymen/go-whatsapp"

const WaDocTmpPath = "temp/wa_files/%v.%v"

// type waHandler struct {
// 	c *whatsapp.Conn
// }

func IsMessageAllowedToSend(msg string) bool {
	//replace whatsapp symbol
	r := strings.NewReplacer("*", "",
		"#", "",
		"_", "")
	msg = r.Replace(msg)

	rules := []string{"^\\*\\[(|[A-Za-z\\-\\s]+)TRELLO(|[A-Za-z\\-\\s]+)\\]\\*", "^UNIQ POS.+", "^Your E-Receipt.+", "^NOTA PEMBELIAN", "^INTERVIEW INVITATION", "^UNIQ-CRM", "^\\[TASK REMINDER\\]", "^KODE PESANAN", "^\\[CUSTOMER INFORMATION\\]"}
	for _, rule := range rules {
		r, _ := regexp.Compile(rule)
		if r.MatchString(msg) {
			return false
		}
	}

	return true
}

// //HandleError needs to be implemented to be a valid WhatsApp handler
// func (h *waHandler) HandleError(err error) {
// 	fmt.Fprintf(os.Stderr, "%v", err)
// 	if err.Error() != "error processing data: received invalid data" {
// 		fmt.Printf("whatsapp error : '%s'", err.Error())
// 	}
// 	if e, ok := err.(*whatsapp.ErrConnectionFailed); ok {
// 		log.Printf("Connection failed, underlying error: %v", e.Err)
// 		log.Println("Waiting 30sec...")
// 		<-time.After(30 * time.Second)
// 		log.Println("Reconnecting...")
// 		err := h.c.Restore()
// 		if err != nil {
// 			log.Fatalf("Restore failed: %v", err)
// 		}
// 	} else {
// 		log.Printf("whatsapp error occoured: %v\n", err)
// 	}
// }

// func (*waHandler) HandleTextMessage(message whatsapp.TextMessage) {
// 	if message.Info.Timestamp > timeStart {
// 		log2.Info("\nbot receive new chat : %s | To : %s | Id : %s | QuotedId : %s | time : %v | time server : %v",
// 			message.Text, message.Info.RemoteJid, message.Info.Id, message.ContextInfo.QuotedMessageID,
// 			message.Info.Timestamp, time.Now().Unix())

// 		//sometimes old message also triggered this function
// 		//to handle, check if message is old than 3 hours, skip
// 		if time.Since(time.Unix(utils.ToInt64(message.Info.Timestamp), 0)).Hours() > 3 {
// 			log2.Info(">>> SKIP...")
// 			return
// 		}

// 		if message.ContextInfo.QuotedMessageID != "" {
// 			log2.Info("info quote : %v", utils.SimplyToJson(message.ContextInfo.QuotedMessage))
// 		}

// 		mention := "@6281717172171 "
// 		if strings.HasSuffix(message.Info.RemoteJid, groupSuffix) {
// 			if strings.HasPrefix(message.Text, mention) {
// 				SendWhatsApp(strings.Replace(message.Info.RemoteJid, groupSuffix, "", -1), GetSimSimiAnswer(strings.Replace(message.Text, mention, "", -1)), "")
// 			}
// 		} else {
// 			phone := strings.Replace(message.Info.RemoteJid, pmSuffix, "", -1)
// 			if phone == "status@broadcast" {
// 				fmt.Println("status broadcast is not recorded..")
// 				return
// 			}
// 			//Chat bot
// 			//resp, err := webhook.DialogFlowRequest(phone, message.Text)
// 			//fmt.Println("resp : ", resp)
// 			//if err == nil && resp != ""{
// 			//	SendWhatsApp(phone, resp)
// 			//}else{
// 			//	fmt.Println("Webhook resp error : ", err)
// 			//}

// 			if message.Info.FromMe && !IsMessageAllowedToSend(message.Text) {
// 				fmt.Println("message is not allowed to send : \n", utils.TakeMax(strings.Replace(message.Text, "\n", "  ", -1), 50))
// 				return
// 			}

// 			PushMessage(WhatsAppChat{
// 				Sender:          phone,
// 				TextMessage:     TextMessage{Message: message.Text},
// 				IsFromMe:        message.Info.FromMe,
// 				MessageId:       message.Info.Id,
// 				TimeStamp:       utils.ToInt64(message.Info.Timestamp * 1000),
// 				MessageQuotedId: message.ContextInfo.QuotedMessageID,
// 			})
// 		}
// 	}
// }

// func (*waHandler) HandleImageMessage(message whatsapp.ImageMessage) {
// 	if message.Info.Timestamp > timeStart {
// 		log2.Info("Bot receive Image Message... caption: '%s'", message.Caption)
// 		if message.Info.RemoteJid == "status@broadcast" {
// 			fmt.Println("status broadcast is not recorded..")
// 			return
// 		}
// 		//sometimes old message also triggered this function
// 		//to handle, check if message is old than 3 hours, skip
// 		if time.Since(time.Unix(utils.ToInt64(message.Info.Timestamp), 0)).Hours() > 3 {
// 			log2.Info(">>> SKIP...")
// 			return
// 		}

// 		data, err := message.Download()
// 		if err != nil {
// 			log2.Info("downloading image error - %v ", err)
// 			return
// 		}
// 		//filename := fmt.Sprintf("%v/%v.%v", os.TempDir(), message.Info.Id, strings.Split(message.Type, "/")[1])
// 		filename := fmt.Sprintf(WaDocTmpPath, message.Info.Id, strings.Split(message.Type, "/")[1])
// 		file, err := os.Create(filename)
// 		defer file.Close()
// 		if err != nil {
// 			log2.Info("error create file to %s - %v", filename, err)
// 			return
// 		}
// 		_, err = file.Write(data)
// 		if err != nil {
// 			log2.Info("error write file to %s - %v", filename, err)
// 			return
// 		}
// 		//fmt.Printf("%v %v\n\timage reveived, saved at:%v\n", message.Info.Timestamp, message.Info.RemoteJid, filename)

// 		phone := strings.Replace(message.Info.RemoteJid, pmSuffix, "", -1)
// 		if message.Caption == "" || (!message.Info.FromMe && IsMessageAllowedToSend(message.Caption)) {
// 			UploadFile(WhatsAppChat{
// 				Sender:       phone,
// 				IsFromMe:     message.Info.FromMe,
// 				MessageId:    message.Info.Id,
// 				ImageMessage: ImageMessage{Caption: message.Caption, ImgPath: filename},
// 				TimeStamp:    utils.ToInt64(message.Info.Timestamp) * int64(1000),
// 			})
// 		} else {
// 			fmt.Println("Chat forbid to send, IsMessageAllowedToSend: ", IsMessageAllowedToSend(message.Caption))
// 		}
// 	}
// }

// func (*waHandler) HandleVideoMessage(message whatsapp.VideoMessage) {
// 	if message.Info.Timestamp <= timeStart {
// 		return
// 	}

// 	log2.Info("Bot receive video. title : %s - type : %s ", message.Caption, message.Type)
// 	if message.Info.RemoteJid == "status@broadcast" {
// 		fmt.Println("status broadcast is not recorded..")
// 		return
// 	}

// 	isFileDownloaded := true
// 	data, err := message.Download()
// 	if log2.IfError(err) {
// 		isFileDownloaded = false
// 	}

// 	extension := strings.Split(message.Type, "/")[1]
// 	//filename := fmt.Sprintf("%v/%v.%v", os.TempDir(), message.Info.Id, extension)
// 	filename := fmt.Sprintf(WaDocTmpPath, message.Info.Id, extension)
// 	log2.Info("filename : %s ", filename)
// 	file, err := os.Create(filename)

// 	defer file.Close()
// 	if log2.IfError(err) {
// 		isFileDownloaded = false
// 	}
// 	_, err = file.Write(data)
// 	if log2.IfError(err) {
// 		isFileDownloaded = false
// 	}

// 	phone := strings.Replace(message.Info.RemoteJid, pmSuffix, "", -1)
// 	if isFileDownloaded {
// 		UploadFile(WhatsAppChat{
// 			Sender:       phone,
// 			TextMessage:  TextMessage{Message: fmt.Sprintf("*<< VIDEO MESSAGE_ >>*  \n%s", message.Caption)},
// 			IsFromMe:     message.Info.FromMe,
// 			MessageId:    message.Info.Id,
// 			VideoMessage: VideoMessage{TempFilePath: filename},
// 			TimeStamp:    utils.ToInt64(message.Info.Timestamp) * int64(1000),
// 		})
// 	} else {
// 		PushMessage(WhatsAppChat{
// 			Sender:      phone,
// 			TextMessage: TextMessage{Message: fmt.Sprintf("*<< _VIDEO MESSAGE_ >>* \n%s\n [backend fail to download]", message.Caption)},
// 			IsFromMe:    message.Info.FromMe,
// 			MessageId:   message.Info.Id,
// 			TimeStamp:   utils.ToInt64(message.Info.Timestamp) * int64(1000),
// 		})
// 	}
// }

// func (*waHandler) HandleDocumentMessage(message whatsapp.DocumentMessage) {
// 	if message.Info.Timestamp <= timeStart {
// 		return
// 	}

// 	log2.Info("Bot receive document. title : %s - type : %s - fileName : %s", message.Title, message.Type, message.FileName)
// 	if time.Since(time.Unix(utils.ToInt64(message.Info.Timestamp), 0)).Hours() > 3 {
// 		log2.Info(">>> SKIP...")
// 		return
// 	}

// 	isFileDownloaded := true
// 	data, err := message.Download()
// 	if log2.IfError(err) {
// 		isFileDownloaded = false
// 	}
// 	extension := strings.Split(message.Type, "/")[1]
// 	if strings.Contains(extension, "sheet") {
// 		extension = "xls"
// 	} else if strings.Contains(extension, "separated") {
// 		extension = "csv"
// 	}
// 	//filename := fmt.Sprintf("%v/%v.%v", os.TempDir(), message.Info.Id, extension)
// 	filename := fmt.Sprintf(WaDocTmpPath, message.Info.Id, extension)
// 	log2.Info("filename : %s ", filename)
// 	file, err := os.Create(filename)
// 	defer file.Close()
// 	if log2.IfError(err) {
// 		isFileDownloaded = false
// 	}
// 	_, err = file.Write(data)
// 	if log2.IfError(err) {
// 		isFileDownloaded = false
// 	}

// 	phone := strings.Replace(message.Info.RemoteJid, pmSuffix, "", -1)
// 	if isFileDownloaded {
// 		UploadFile(WhatsAppChat{
// 			Sender:          phone,
// 			TextMessage:     TextMessage{Message: fmt.Sprintf("*<< DOCUMENT MESSAGE_ >>*  \n%s.%s", message.Title, extension)},
// 			IsFromMe:        message.Info.FromMe,
// 			MessageId:       message.Info.Id,
// 			DocumentMessage: DocumentMessage{TempFilePath: filename},
// 			TimeStamp:       utils.ToInt64(message.Info.Timestamp) * int64(1000),
// 		})
// 	} else {
// 		PushMessage(WhatsAppChat{
// 			Sender:      phone,
// 			TextMessage: TextMessage{Message: "*<< _DOCUMENT MESSAGE_ >>* \n\n[backend fail to download]"},
// 			IsFromMe:    message.Info.FromMe,
// 			MessageId:   message.Info.Id,
// 			TimeStamp:   utils.ToInt64(message.Info.Timestamp) * int64(1000),
// 		})
// 	}
// }

// func (*waHandler) HandleAudioMessage(message whatsapp.AudioMessage) {
// 	if message.Info.Timestamp <= timeStart {
// 		return
// 	}

// 	log2.Info("Bot receive audio")
// 	phone := strings.Replace(message.Info.RemoteJid, pmSuffix, "", -1)
// 	PushMessage(WhatsAppChat{
// 		Sender:      phone,
// 		TextMessage: TextMessage{Message: "*<< _AUDIO MESSAGE_ >>*"},
// 		IsFromMe:    message.Info.FromMe,
// 		MessageId:   message.Info.Id,
// 		TimeStamp:   utils.ToInt64(message.Info.Timestamp) * int64(1000),
// 	})
// }

// func (*waHandler) HandleJsonMessage(message string) {
// 	//utils.SendNotifServerToSlack(fmt.Sprintf("wa bot receive json : '%s'", message))
// 	log2.Info("Bot receive json message... %s", message)
// 	callIdentifier := "[\"Call\","
// 	if strings.Contains(message, "[\"Call\",") && strings.Contains(message, "\"type\":\"offer\"") {
// 		msgJson := strings.Replace(message, callIdentifier, "[", -1)
// 		var messageModelArray JsonMessage
// 		err := json.Unmarshal([]byte(msgJson), &messageModelArray)
// 		log2.IfError(err)

// 		if len(messageModelArray) == 0 {
// 			return
// 		}

// 		msgModel := messageModelArray[0]
// 		phone := strings.Replace(msgModel.From, callSuffix, "", -1)

// 		PushMessage(WhatsAppChat{
// 			Sender:          phone,
// 			TextMessage:     TextMessage{Message: "*<< _CALLING..._ >>*"},
// 			IsFromMe:        false,
// 			MessageId:       msgModel.ID,
// 			MessageQuotedId: "",
// 			TimeStamp:       time.Now().Unix() * 1000,
// 		})
// 	} else {
// 		var modelMsg []interface{}
// 		err := json.Unmarshal([]byte(message), &modelMsg)
// 		if err != nil {
// 			return
// 		}

// 		fmt.Println("total : ", len(modelMsg))
// 		if len(modelMsg) >= 2 {
// 			if modelMsg[0] == "Msg" {
// 				if data, ok := modelMsg[1].(map[string]interface{}); ok {
// 					if (utils.ToString(data["ack"]) == "3" || utils.ToString(data["ack"]) == "3.00") && (data["cmd"] == "acks" || data["cmd"] == "ack") {
// 						AckMessage(data)
// 					}
// 				}
// 			} else if modelMsg[0] == "Presence" {
// 				if data, ok := modelMsg[1].(map[string]interface{}); ok {
// 					fmt.Println(data["id"], "is: ", data["type"])
// 					if utils.ToString(data["type"]) == "composing" {
// 						phone := strings.Replace(utils.ToString(data["id"]), callSuffix, "", -1)
// 						UpdateTyping(phone, phone, true)
// 					}
// 				}
// 			}
// 		}
// 	}
// }

// func (*waHandler) HandleContactMessage(message whatsapp.ContactMessage) {
// 	if message.Info.Timestamp <= timeStart {
// 		return
// 	}

// 	log2.Info("receiving contact message : %v", utils.SimplyToJson(message))
// 	sender := strings.Replace(message.Info.RemoteJid, pmSuffix, "", -1)
// 	phones := make([]string, 0)

// 	//r, _ := regexp.Compile("TEL(;[a-zA-Z=]+([\\d]+|)|):\\+[\\d\\s-]+")
// 	//res := r.FindAllString(message.Vcard, -1)
// 	//fmt.Println(len(res), " results : ", res)
// 	//
// 	//for _, tel := range res {
// 	//	r, _ = regexp.Compile("TEL(;[a-zA-Z=]+([\\d]+|)|):")
// 	//	phones = append(phones, strings.TrimSpace(r.ReplaceAllString(tel, "")))
// 	//}

// 	//index 0: all word
// 	//index 1: type of phone (mobile,work,home, etc..) - can be empty
// 	//index 2: wa id (blank indicating the number is not registered as whatsapp number)
// 	//index 3: the phone number
// 	//r, _ := regexp.Compile(`TEL(?:;type=([a-zA-Z]+)|)(?:;waid=(\d+)|):(\+[\d\s-]+)`)
// 	//for _, tel := range r.FindAllStringSubmatch(message.Vcard, -1) {
// 	//	typePhone, waId, phone := tel[1], tel[2], tel[3]
// 	//	phones = append(phones, phone)
// 	//	fmt.Println(phone, "type:", typePhone, "isWa?", waId != "")
// 	//}
// 	phones = utils.ExtractVCard(message.Vcard).Phones

// 	//if len(phones) == 0 {
// 	//	r, _ := regexp.Compile("waid=[\\d]+")
// 	//	res := r.FindAllString(message.Vcard, -1)
// 	//	if len(res) > 0 {
// 	//		phones = append(phones, strings.Replace(res[0], "waid=", "", -1))
// 	//	}
// 	//}

// 	contactInfo := fmt.Sprintf(
// 		"Name  : %s \n"+
// 			"Phone : %s", message.DisplayName, strings.Join(phones, ",\n"))

// 	PushMessage(WhatsAppChat{
// 		Sender:      sender,
// 		TextMessage: TextMessage{Message: "*<< _CONTACT MESSAGE_ >>*\n\n" + contactInfo},
// 		IsFromMe:    message.Info.FromMe,
// 		MessageId:   message.Info.Id,
// 		TimeStamp:   utils.ToInt64(message.Info.Timestamp) * int64(1000),
// 	})
// }

// func (h *waHandler) HandleNewContact(contact whatsapp.Contact) {
// 	fmt.Println("HandleNewContact: ", contact)
// }

// func (h *waHandler) HandleLocationMessage(message whatsapp.LocationMessage) {
// 	if message.Info.Timestamp <= timeStart {
// 		return
// 	}

// 	msg, _ := json.Marshal(message)
// 	log2.Info("Bot receive location: %s", string(msg))

// 	filename := fmt.Sprintf(WaDocTmpPath, message.Info.Id, "jpg")
// 	err := utils.DownloadFileBase64(message.JpegThumbnail, filename)
// 	log2.IfError(err)

// 	if message.Address == message.Name {
// 		message.Name = ""
// 	}

// 	phone := strings.Replace(message.Info.RemoteJid, pmSuffix, "", -1)
// 	locationUrl := fmt.Sprintf("https://www.google.com/maps/search/?api=1&query=%v,%v", message.DegreesLatitude, message.DegreesLongitude)
// 	msgText := fmt.Sprintf("<< LOCATION >>\n\n%s\n\n%s%s", locationUrl, strings.ToUpper(message.Name), message.Address)

// 	if err == nil {
// 		UploadFile(WhatsAppChat{
// 			Sender:       phone,
// 			IsFromMe:     message.Info.FromMe,
// 			MessageId:    message.Info.Id,
// 			ImageMessage: ImageMessage{Caption: strings.TrimSpace(msgText), ImgPath: filename},
// 			TimeStamp:    utils.ToInt64(message.Info.Timestamp) * int64(1000),
// 		})
// 	} else {
// 		PushMessage(WhatsAppChat{
// 			Sender:          phone,
// 			TextMessage:     TextMessage{Message: strings.TrimSpace(msgText)},
// 			IsFromMe:        message.Info.FromMe,
// 			MessageId:       message.Info.Id,
// 			TimeStamp:       utils.ToInt64(message.Info.Timestamp * 1000),
// 			MessageQuotedId: message.ContextInfo.QuotedMessageID,
// 		})
// 	}
// }

// //func (h *waHandler) HandleBatteryMessage(msg whatsapp.BatteryMessage) {
// //	fmt.Println("HandleBatteryMessage: ",msg)
// //}

// func AckMessage(data map[string]interface{}) {
// 	fmt.Println("from", data["from"], "to", data["to"])
// 	phone := strings.Replace(utils.ToString(data["to"]), "@c.us", "", -1)

// 	if ids, okId := data["id"].([]interface{}); okId {
// 		fmt.Println("ids : ", ids)
// 		for _, id := range ids {
// 			UpdateChatStatus(phone, utils.ToString(id), "read")
// 		}
// 	} else if id, ok := data["id"].(interface{}); ok {
// 		UpdateChatStatus(phone, utils.ToString(id), "read")
// 	}
// }

// type JsonMessage []struct {
// 	ID       string `json:"id"`
// 	Type     string `json:"type"`
// 	From     string `json:"from"`
// 	Platform string `json:"platform"`
// 	Version  []int  `json:"version"`
// }
