package auth

import (
	"fmt"
	"os"
	"regexp"
	"strings"
	"time"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/uniq-bot/core/log"
	utils "gitlab.com/uniqdev/backend/uniq-bot/util"
	"gitlab.com/uniqdev/backend/uniq-bot/util/stream"
)

func ValidateIp(next fasthttp.RequestHandler) fasthttp.RequestHandler {
	return func(ctx *fasthttp.RequestCtx) {
		timeStart := time.Now()
		ipAddress := utils.GetIpAddress(ctx)
		ctx.SetContentType("application/json")
		if ipAddress == "127.0.0.1" || IsLocalOrPrivateIp(ipAddress) {
			next(ctx)
		} else {
			if IsIPAllowed(ctx) || isValidAuthorization(ctx) {
				next(ctx)
			} else {
				ctx.SetStatusCode(fasthttp.StatusUnauthorized)
				log.Warn("Someone request to messenger-gateway from unrecognized IP - '%s'", ipAddress)
				log.Warn("auth : %s", ctx.Request.Header.Peek("Authorization"))
			}
		}

		//log request...
		bodySize := len(ctx.Response.Body())
		bodyUnit := "B"
		if bodySize/1000 < 1000 {
			bodyUnit = "KB"
			bodySize = bodySize / 1000
		} else if bodySize/1000 >= 1000 {
			bodyUnit = "MB"
			bodySize = bodySize / 1000000
		}
		fmt.Printf("%d | %v | %s | %d %s | %s | %s\n", ctx.Response.StatusCode(), time.Since(timeStart), string(ctx.Method()), bodySize, bodyUnit, string(ctx.URI().Path()), ipAddress)
	}
}

func IsIPAllowed(ctx *fasthttp.RequestCtx) bool {
	ipAddress := utils.GetIpAddress(ctx)
	ips, err := utils.ReadFile("whitelist_ip.txt")
	if err != nil {
		log.Warn("read whitelist error: %v", err)
	}

	whiteListIp := strings.Split(ips, ",")
	for _, ip := range whiteListIp {
		log.Info("'%s'", ip)
	}
	return stream.Any(whiteListIp, ipAddress)
}

func IsLocalOrPrivateIp(ipAddress string) bool {
	//private or local ips are :
	/*127.  0.0.0 – ***************     ********* /8
	10.  0.0.0 –  **************      10.0.0.0 /8
	172. 16.0.0 – 172. 31.255.255    ********** /12
	*********** – ***************   *********** /16*/
	//(^127\.)|(^10\.)|(^172\.1[6-9]\.)|(^172\.2[0-9]\.)|(^172\.3[0-1]\.)|(^192\.168\.)
	pattern := `(^127\.)|(^172\.1[6-9]\.)|(^172\.2[0-9]\.)|(^172\.3[0-1]\.)`
	r, _ := regexp.Compile(pattern)
	return r.MatchString(ipAddress)
}

func isValidAuthorization(ctx *fasthttp.RequestCtx) bool {
	auth := string(ctx.Request.Header.Peek("Authorization"))
	return auth == os.Getenv("auth_key")
}
