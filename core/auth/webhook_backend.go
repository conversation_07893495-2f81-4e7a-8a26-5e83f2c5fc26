package auth

import (
	"fmt"
	"golang.org/x/oauth2/google"
	"golang.org/x/oauth2/jwt"
	"io/ioutil"
)

var basicAuth *BasicAuthBackend = nil

func InitWebHookAuthBackend() *BasicAuthBackend {
	if basicAuth == nil {
		basicAuth = &BasicAuthBackend{
			Username: "uniqdev",
			Password: "xCJxTEFgwCBLtJNyVSzMH9mj",
		}
	}
	return basicAuth
}

func OauthGoogle() *jwt.Config {
	data, err := ioutil.ReadFile("config/auth/uniq-187911-3c441dc96451.json")
	if err != nil {
		fmt.Println("Read json for auth error : ", err)
	}
	conf, err := google.JWTConfigFromJSON(data, "https://www.googleapis.com/auth/cloud-platform")
	if err != nil {
		fmt.Println("Oauth Google Error ", err)
	}
	return conf
}
