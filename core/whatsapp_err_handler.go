package core

import (
	"fmt"
	"gitlab.com/uniqdev/backend/uniq-bot/core/log"
	"strings"
)

var timeOutMessages = make(map[string]bool)

func getId(phone, msg string) string {
	id := fmt.Sprintf("%s|%s", phone, msg)
	id = strings.ReplaceAll(id, " ", "")
	id = strings.ReplaceAll(id, "\n", "")
	return id
}

func saveAsTimeOutMessage(phone, msg string) {
	id := getId(phone, msg)
	timeOutMessages[id] = true
	log.Info("added to timeOut message: %s\nwith id: %s", phone, id)
}

func removeTimeOutMessage(phone, msg string) {
	delete(timeOutMessages, getId(phone, msg))
}

func removeAllTimeOutMessage() {
	if len(timeOutMessages) > 0 {
		fmt.Println("removing all timeout message")
	}
	timeOutMessages = make(map[string]bool)
}

func isMessageInTimeOutList(phone, msg string) bool {
	id := getId(phone, msg)
	log.Info("message in timeout list? '%s' -> %v\nwith id: %s", phone, timeOutMessages[id], id)
	return timeOutMessages[id]
}
