package core

// "github.com/Rhymen/go-whatsapp"

// var timeStart uint64
// var wac *whatsapp.Conn

// const pmSuffix = "@s.whatsapp.net"
// const callSuffix = "@c.us"
// const groupSuffix = "@g.us"
// const waVersionLogPath = "/temp/sessions/wa_version.txt" //we will keep the latest version in here (programmatically)
// var officialPhone = "6281717172171"                      //"6281717172171"

// var waVersion = version{2, 2147, 16}
// var restartToken = ""

// type WAThumbUrl struct {
// 	EURL   string `json:"eurl"`
// 	Tag    string `json:"tag"`
// 	Status int64  `json:"status"`
// }

// func InitSession() {
// 	_ = godotenv.Load()
// 	timeStart = uint64(time.Now().Unix())
// 	log.Info("wa type : '%s'", os.Getenv("wa_type"))
// 	if os.Getenv("OFFICIAL_PHONE") != "" {
// 		officialPhone = os.Getenv("OFFICIAL_PHONE")
// 	}

// 	if os.Getenv("wa_type") == "dynamic" {
// 		fmt.Println("_____ WA Dynamic _____")
// 	} else {
// 		//initOfficialWhatsApp()
// 		fmt.Println("_____ WA Official _____")
// 		restoreSession()
// 	}
// }

// func initWAClient() error {
// 	log.Info("initializing wac...")
// 	timeOut := 20 * time.Second
// 	var err error
// 	wac, err = whatsapp.NewConn(timeOut)
// 	if err != nil {
// 		fmt.Println("can not establish new connection")
// 		return err
// 	}

// 	//get from file if exist
// 	versionLog, err := ioutil.ReadFile(waVersionLogPath)
// 	if err == nil {
// 		versions := strings.Split(string(versionLog), ".")
// 		if len(versions) == 3 {
// 			waVersion.major = utils.ToInt(versions[0])
// 			waVersion.minor = utils.ToInt(versions[1])
// 			waVersion.patch = utils.ToInt(versions[2])
// 		}
// 	}

// 	//TODO: set client version
// 	//wac.SetClientVersion(waVersion.major, waVersion.minor, waVersion.patch)
// 	//
// 	//err = wac.SetClientName("WhatsApp Web", "wa web", fmt.Sprintf("%d.%d.%d", waVersion.major, waVersion.major, waVersion.patch))
// 	//log.IfError(err)

// 	return nil
// }

// func checkVersion() {
// 	v := wac.GetClientVersion()
// 	clientVersion := &version{major: v[0], minor: v[1], patch: v[2]}
// 	fmt.Printf("Client has version %d.%d.%d\n", clientVersion.major, clientVersion.minor, clientVersion.patch)

// 	v, err := whatsapp.CheckCurrentServerVersion()
// 	serverVersion := &version{major: v[0], minor: v[1], patch: v[2]}
// 	if err != nil {
// 		fmt.Println(err)
// 		return
// 	}
// 	fmt.Printf("Server has version %d.%d.%d\n", serverVersion.major, serverVersion.minor, serverVersion.patch)

// 	format := "%d.%d.%d"
// 	if fmt.Sprintf(format, clientVersion.major, clientVersion.minor, clientVersion.patch) != fmt.Sprintf(format, serverVersion.major, serverVersion.minor, serverVersion.patch) {
// 		log.ToSlack(fmt.Sprintf("Client has version %d.%d.%d\n"+
// 			"Server has version %d.%d.%d\n",
// 			clientVersion.major, clientVersion.minor, clientVersion.patch,
// 			serverVersion.major, serverVersion.minor, serverVersion.patch))

// 		waVersion.major = serverVersion.major
// 		waVersion.minor = serverVersion.minor
// 		waVersion.patch = serverVersion.patch

// 		err = ioutil.WriteFile(waVersionLogPath, []byte(fmt.Sprintf("%d.%d.%d", serverVersion.major, serverVersion.minor, serverVersion.patch)), 0644)
// 		if !log.IfError(err) {
// 			restoreSession()
// 		}
// 	}
// }

// func LoginOfficialAsync() map[string]interface{} {
// 	qr := make(chan string)
// 	timeOut := 20 * time.Second

// 	fmt.Println("login... wac : ", wac)

// 	//if wa is logged in, pass the process
// 	if wac != nil {
// 		if pass, _ := wac.AdminTest(); pass {
// 			fmt.Printf("login process pass, whatsapp is logged in with phone : %s\n", wac.Info.Wid)
// 			return map[string]interface{}{
// 				"status": "WhatsApp is logged in already",
// 			}
// 		}
// 	}

// 	go func() {
// 		err := restoreSession()
// 		if err == nil {
// 			qr <- "session restored"
// 			return
// 		}

// 		fmt.Println("wac: ", wac)
// 		if wac == nil {
// 			err = initWAClient()
// 			if log.IfError(err) {
// 				return
// 			}
// 		}
// 		session, err := wac.Login(qr)
// 		if !log.IfError(err) {
// 			log.Info("login success... ")

// 			//check if the phone number is the official phone number
// 			loggedInPhone := strings.Replace(wac.Info.Wid, callSuffix, "", -1)
// 			if loggedInPhone != officialPhone {
// 				log.Error("whatsapp loged in from unofficial phone number, which : %s", loggedInPhone)
// 				log.IfError(wac.Logout())
// 				return
// 			}

// 			err = writeSession(session)
// 			log.IfError(err)

// 			//Add handler
// 			wac.AddHandler(&waHandler{wac})
// 			checkVersion()
// 		}
// 	}()

// 	qrCode := <-qr
// 	log.Info("your qr : %s", qrCode)

// 	fileName, err := utils.GenerateQrFile(qrCode)
// 	if err == nil {
// 		SendFileToSlack(fileName)
// 	}

// 	params := url.Values{}
// 	params.Add("cht", "qr")
// 	params.Add("chs", "250x250")
// 	params.Add("chl", qrCode)
// 	urlParam := "cht=qr"
// 	urlParam += "&chs=250x250"
// 	urlParam += "&chl=" + qrCode

// 	baseUrl, err := url.Parse("https://chart.googleapis.com")
// 	log.IfError(err)
// 	baseUrl.Path = "chart"
// 	baseUrl.RawQuery = urlParam
// 	log.Info(baseUrl.String())

// 	whatsAppWebUrl := fmt.Sprintf("https://chart.googleapis.com/chart?cht=qr&chs=250x250&chl=%s", qrCode)
// 	log.ToSlack(whatsAppWebUrl)

// 	return map[string]interface{}{
// 		"expired":       time.Now().Add(timeOut).Unix() * 1000,
// 		"qr_code":       qrCode,
// 		"qr_code_image": whatsAppWebUrl,
// 	}
// }

// func WhatsAppLoginStatus() map[string]interface{} {
// 	if wac == nil {
// 		log.Info("whatsapp is uninitialized")
// 		return map[string]interface{}{"status": "whatsapp is uninitialize"}
// 	}

// 	defer func() {
// 		if r := recover(); r != nil {
// 			fmt.Println("Recovered in f", r)
// 		}
// 	}()

// 	fmt.Printf("wa is connected : %v\n", wac.Info.Connected)
// 	fmt.Printf("wa last seen    : %s\n", wac.ServerLastSeen.Add(7*time.Hour).Format("01/02/2006 15:04:05"))
// 	fmt.Printf("wa login phone  : %s\n", wac.Info.Wid)

// 	return map[string]interface{}{
// 		"connected": wac.Info.Connected,
// 	}
// }

// func initOfficialWhatsApp() {
// 	fmt.Println("____ Init WA Bot ____")
// 	timeStart = uint64(time.Now().Unix())
// 	fmt.Println("Timestart : ", timeStart)

// 	//create new WhatsApp connection
// 	var err error
// 	wac, err = whatsapp.NewConn(20 * time.Second)

// 	if err != nil {
// 		fmt.Fprintf(os.Stderr, "error creating connection: %v\n", err)
// 		return
// 	}

// 	//this help to handle error
// 	wac.SetClientVersion(0, 4, 1307)

// 	//Add handler
// 	wac.AddHandler(&waHandler{wac})

// 	//login
// 	err = login(wac)
// 	if err != nil {
// 		fmt.Fprintf(os.Stderr, "error logging in: %v\n", err)
// 		return
// 	}
// }

// func GetPhotoProfile(phone string) (string, error) {
// 	if wac == nil {
// 		return "", errors.New("wa client uninitialized")
// 	}
// 	profilePicThumb, err := wac.GetProfilePicThumb(phone + "@s.whatsapp.net")
// 	if err != nil {
// 		fmt.Println("Getting profile pic error - ", err)
// 		return "", err
// 	}

// 	profilePic := <-profilePicThumb
// 	fmt.Println("Profile Pic : ", profilePic)

// 	thumbnail := WAThumbUrl{}
// 	err = json.Unmarshal([]byte(profilePic), &thumbnail)
// 	utils.CheckErr(err)

// 	return thumbnail.EURL, nil
// }

// func testPing() {
// 	isSuccess, err := wac.AdminTest()
// 	fmt.Println("[PING] test ping: ", isSuccess, "--", err)
// 	if isSuccess && err == nil {
// 		removeAllTimeOutMessage()
// 	}
// 	//msg := whatsapp.TextMessage{
// 	//	Info: whatsapp.MessageInfo{
// 	//		RemoteJid: "<EMAIL>",
// 	//	},
// 	//	Text: "p",
// 	//}
// 	//
// 	//_, err := wac.Send(msg)
// 	//if err == nil {
// 	//	removeAllTimeOutMessage()
// 	//}
// }

// func sendUsingCustomWebApp(phone, messsage string) error {
// 	request := request2.HttpRequest{}
// 	request.Method = "POST"
// 	request.Url = "http://************:5000/whatsapp/send/text"
// 	request.PostRequest.Form = map[string]string{
// 		"to":      phone,
// 		"message": messsage,
// 	}

// 	body, err := request.Execute()
// 	if err != nil {
// 		fmt.Println("sending wa to", phone, " error ", err)
// 		return err
// 	}

// 	//fmt.Println(string(body))
// 	var resp map[string]interface{}
// 	err = json.Unmarshal(body, &resp)
// 	if utils.CheckErr(err) {
// 		fmt.Println("parsing json from WhatsApp web app error - ", err)
// 		return err
// 	}

// 	//if resp["status"] != nil && resp["status"] == true {
// 	//	return nil
// 	//} else {
// 	//	fmt.Println("result : ", string(body))
// 	//	return errors.New("server assume that message not send successfully")
// 	//}

// 	if resp["status"] != nil && resp["status"] == true {

// 	} else {
// 		log.Error("send WA considered as error. result : %s", string(body))
// 		fmt.Println("send WA considered as error. result : ", string(body))
// 		if resp["message"] == "'_AppCtxGlobals' object has no attribute 'driver'" {
// 			return errors.New(utils.ToString(resp["message"]))
// 		}
// 	}

// 	return nil

// }

// func sendUsingWaBoxApp(phone, messsage string) error {
// 	request := request2.HttpRequest{}
// 	request.Method = "POST"
// 	request.Url = "https://www.waboxapp.com/api/send/chat"
// 	request.PostRequest.Form = map[string]string{
// 		"token":      "1686f3e0281936877077d6ce89e577b35b07dcbbbb473",
// 		"uid":        "6281717172171",
// 		"to":         phone,
// 		"custom_uid": fmt.Sprintf("msg-%d", time.Now().Unix()),
// 		"text":       messsage,
// 	}

// 	request.Header = map[string]interface{}{
// 		"Content-Type": "application/x-www-form-urlencoded",
// 	}

// 	body, err := request.Execute()
// 	if err != nil {
// 		fmt.Println("sending wa to", phone, " error ", err)
// 		return err
// 	}

// 	fmt.Println("body ", string(body))

// 	return nil
// }

// func sendMediaUsingCustomWebApp(data map[string]string) {
// 	form := map[string]string{
// 		"caption":   data["caption"],
// 		"to":        data["phone"],
// 		"file_url":  data["file_url"],
// 		"file_type": data["file_type"],
// 	}
// 	request := request2.HttpRequest{}
// 	request.Url = "http://************:5000/whatsapp/send/media"
// 	request.MultipartRequest.FilePath = data["file_path"]
// 	request.MultipartRequest.FileParam = "file"
// 	request.MultipartRequest.Form = form

// 	fmt.Printf("filepath '%s' \n", data["file_path"])
// 	if data["file_path"] == "" {
// 		request.Method = "POST"
// 		request.PostRequest.Form = form
// 	}

// 	res, err := request.Execute()
// 	if err == nil {
// 		fmt.Println("sending image to wa success...")
// 		fmt.Println(string(res))
// 	}
// }

// func login(wac *whatsapp.Conn) error {
// 	//load saved session
// 	fmt.Println("read session...")
// 	session, err := readSession()
// 	if err == nil {
// 		fmt.Println("restoring session...")
// 		//restore session
// 		session, err = wac.RestoreWithSession(session)
// 		//session, err = wac.restoreSession(session)
// 		if err != nil {
// 			//os.Remove("config/whatsappSession.gob")
// 			//fmt.Println("Delete session : ", "config/whatsappSession.gob")
// 			return fmt.Errorf("restoring failed: %v\n", err)
// 		}
// 		fmt.Println("restoring session success")
// 	} else {
// 		//no saved session -> regular login
// 		qr := make(chan string)
// 		go func() {
// 			qrCode := <-qr
// 			terminal := qrcodeTerminal.New()
// 			terminal.Get(qrCode).Print()
// 			fmt.Println("scan this qr code : ", qrCode)
// 		}()
// 		session, err = wac.Login(qr)
// 		if err != nil {
// 			return err
// 		}
// 	}

// 	//save session
// 	err = writeSession(session)
// 	if utils.CheckErr(err) {
// 		return fmt.Errorf("error saving session: %v\n", err)
// 	}
// 	return nil
// }

// func generateRestartToken() {
// 	//if token already generated, and still valid, skip the creation process
// 	if restartToken != "" {
// 		if isValidRestartToken(restartToken) {
// 			return
// 		}
// 	}

// 	tokenText := utils.ToString(time.Now().Add(60 * time.Minute).Unix())
// 	restartToken = security.Encrypt(tokenText, "restart_token")
// }

// func isValidRestartToken(token string) bool {
// 	if token != restartToken {
// 		return false
// 	}

// 	tokenDec := security.Decrypt(token, "restart_token")
// 	tokenTime := utils.ToInt64(tokenDec)
// 	if tokenTime < time.Now().Unix() {
// 		return false
// 	}

// 	return true
// }

// type version struct {
// 	major int
// 	minor int
// 	patch int
// }
