package core

import (
	"fmt"
	"gitlab.com/uniqdev/backend/uniq-bot/util/request"
)

func SendFileToSlack(file string) {
	//sh "curl https://slack.com/api/files.upload -F token=\""+ ENV['SLACK_ACCESS_TOKEN'] +"\" -F channels=\"prod-crm\" -F title=\"" + file_name + "\" -F filename=\"" + file_name + "\" -F file=@" + full_file_path
	req := request.HttpRequest{
		Method: "POST",
		Url:    "https://slack.com/api/files.upload",
		Header: nil,
		MultipartRequest: request.MultipartRequest{
			FilePath:  file,
			FileParam: "file",
			Form: map[string]string{
				"token":     "****************************************************************************",
				"channels":  "messager-gateway",
				"title":     file,
				"file_name": file,
			},
		},
	}
	_, err := req.Execute()
	if err != nil {
		fmt.Println("sending slack file err: ", err)
		return
	}
}
