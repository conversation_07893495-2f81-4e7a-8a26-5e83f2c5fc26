package database

import (
	"database/sql"
	"fmt"
	_ "github.com/go-sql-driver/mysql"
	"github.com/joho/godotenv"
	"github.com/nanobox-io/golang-scribble"
	"log"
	"os"
)

var db *sql.DB

func init() {
	fmt.Println("__________Init database connection!____________")
	e := godotenv.Load() //Load .env file
	if e != nil {
		fmt.Print(e)
	}

	username := os.Getenv("db_user")
	password := os.Getenv("db_password")
	dbName := os.Getenv("db_name")
	dbHost := os.Getenv("db_host")

	var err error
	source := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s", username, password, dbHost, "3306", dbName)
	db, err = sql.Open("mysql", source)
	if err != nil {
		log.Fatalf("Could not open Db: %v", err)
	}
	db.SetMaxIdleConns(0)
}

func GetConnection() *sql.DB {
	return db
}

func GetDbJson() *scribble.Driver {
	dbJson, err := scribble.New("temp/db", nil)
	if err != nil {
		fmt.Println("Error", err)
	}
	return dbJson
}
