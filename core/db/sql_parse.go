package database

import (
	"database/sql"
	"fmt"
	"strings"
)

func QueryArray(sqlQuery string, args ...interface{}) ([]map[string]interface{}, error) {
	db := GetConnection()
	tableData := make([]map[string]interface{}, 0)

	//stmt, err := db.Prepare(sqlQuery)
	//if err != nil {
	//	return tableData, err
	//}
	//defer stmt.Close()
	//
	//rows, err := stmt.Query(args...)
	//start := time.Now()
	rows, err := db.Query(sqlQuery, args...)
	if err != nil {
		return tableData, err
	}

	//for i := 0; i < len(args); i++{
	//	index := strings.Index(sqlQuery, "?")
	//	sqlQuery = sqlQuery[:index] + args[i].(string) + sqlQuery[index+1:]
	//}
	//fmt.Println(sqlQuery)

	//fmt.Println(sqlQuery)

	//elapsed := time.Since(start)
	//fmt.Println("--- Query (array) took : ", elapsed)

	defer rows.Close()

	columns, err := rows.Columns()
	if err != nil {
		return tableData, nil
	}

	count := len(columns)
	values := make([]interface{}, count)
	scanArgs := make([]interface{}, count)

	for i := range values {
		scanArgs[i] = &values[i]
	}

	for rows.Next() {
		err := rows.Scan(scanArgs...)
		if err != nil {
			return tableData, nil
		}

		entry := make(map[string]interface{})
		for i, col := range columns {
			v := values[i]

			b, ok := v.([]byte)
			if ok {
				entry[col] = string(b)
			} else {
				entry[col] = v
			}
		}
		tableData = append(tableData, entry)
	}

	return tableData, nil
}

func Query(sqlQuery string, args ...interface{}) (map[string]interface{}, error) {
	db := GetConnection()
	entryData := make(map[string]interface{}, 0)

	//stmt, err := db.Prepare(sqlQuery)
	//if err != nil {
	//	return entryData, err
	//}
	//defer stmt.Close()
	//
	//rows, err := stmt.Query(args...)

	//start := time.Now()
	rows, err := db.Query(sqlQuery, args...)
	if err != nil {
		return entryData, err
	}
	//elapsed := time.Since(start)
	//fmt.Println("-- Query took : ", elapsed)

	defer rows.Close()

	//for i := 0; i < len(args); i++{
	//	index := strings.Index(sqlQuery, "?")
	//	sqlQuery = sqlQuery[:index] + args[i].(string) + sqlQuery[index+1:]
	//}
	//fmt.Println(sqlQuery)

	columns, err := rows.Columns()
	if err != nil {
		return entryData, nil
	}

	count := len(columns)
	values := make([]interface{}, count)
	scanArgs := make([]interface{}, count)

	for i := range values {
		scanArgs[i] = &values[i]
	}

	if rows.Next() {
		err := rows.Scan(scanArgs...)
		if err != nil {
			return entryData, nil
		}

		for i, col := range columns {
			v := values[i]

			b, ok := v.([]byte)
			if ok {
				entryData[col] = string(b)
			} else {
				entryData[col] = v
			}
		}
	}

	return entryData, nil
}

func Insert(table string, data map[string]interface{}) (sql.Result, error) {
	values := make([]interface{}, 0)
	query := "INSERT INTO " + table + " ("
	for col, val := range data {
		query += col + ","
		values = append(values, val)
	}
	query = query[:len(query)-1] + ") VALUES (" + strings.Repeat("?, ", len(data)-1) + "?)"
	res, err := GetConnection().Exec(query, values...)
	if err != nil {
		for i := range query {
			if strings.Contains(query, "?") {
				val := fmt.Sprintf("%s", values[i])
				query = strings.Replace(query, "?", val, 1)
			} else {
				break
			}
		}
		fmt.Println("Executing Query Error : ", query)
	}

	return res, err
}

func Update(table string, data map[string]interface{}, where map[string]interface{}) (sql.Result, error) {
	values := make([]interface{}, 0)
	query := "UPDATE " + table + " SET "
	for col, val := range data {
		query += col + " = ?,"
		values = append(values, val)
	}
	query = query[:len(query)-1] + " WHERE "

	for col, val := range where {
		query += col + "=? AND "
		values = append(values, val)
	}
	query = query[:len(query)-4]

	res, err := GetConnection().Exec(query, values...)
	if err != nil {
		fmt.Println("Executing Query Error : ", query)
	}

	return res, err
}

func ResultArray(rows *sql.Rows) ([]map[string]interface{}, error) {
	tableData := make([]map[string]interface{}, 0)
	defer rows.Close()

	columns, err := rows.Columns()
	if err != nil {
		return tableData, nil
	}

	count := len(columns)
	values := make([]interface{}, count)
	scanArgs := make([]interface{}, count)

	for i := range values {
		scanArgs[i] = &values[i]
	}

	for rows.Next() {
		err := rows.Scan(scanArgs...)
		if err != nil {
			return tableData, nil
		}

		entry := make(map[string]interface{})
		for i, col := range columns {
			v := values[i]

			b, ok := v.([]byte)
			if ok {
				entry[col] = string(b)
			} else {
				entry[col] = v
			}
		}
		tableData = append(tableData, entry)
	}

	return tableData, nil
}

func Result(rows *sql.Rows) (map[string]interface{}, error) {
	entryData := make(map[string]interface{}, 0)

	columns, err := rows.Columns()
	if err != nil {
		return entryData, nil
	}

	defer rows.Close()

	count := len(columns)
	values := make([]interface{}, count)
	scanArgs := make([]interface{}, count)

	for i := range values {
		scanArgs[i] = &values[i]
	}

	if rows.Next() {
		err := rows.Scan(scanArgs...)
		if err != nil {
			return entryData, nil
		}

		for i, col := range columns {
			v := values[i]

			b, ok := v.([]byte)
			if ok {
				entryData[col] = string(b)
			} else {
				entryData[col] = v
			}
		}
	}

	return entryData, nil
}
