package log

import (
	"fmt"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/uniq-bot/models"
	"gitlab.com/uniqdev/backend/uniq-bot/util/request"
	"os"
	"runtime"
	"strings"
	"time"
)

const (
	PREFIX_DEBUG = "\033[1;37m[DEBUG]\033[0m"
	PREFIX_INFO  = "\033[1;32m[INFO]\033[0m"
	PREFIX_WARN  = "\033[1;33m[WARN]\033[0m"
	PREFIX_ERROR = "\033[1;31m[ERROR]\033[0m"
)

func Debug(msg string, v ...interface{}) {
	_, fn, line, _ := runtime.Caller(1)
	fmt.Printf(PREFIX_DEBUG+" "+getDate()+" "+fmt.Sprintf("%s:%d", fn, line)+"  :: "+msg+"\n", v...)
}

func Info(msg string, v ...interface{}) {
	_, fn, line, _ := runtime.Caller(1)
	//fmt.Printf(PREFIX_INFO+"  "+getDate()+" "+fmt.Sprintf("%s:%d", fn, line)+"  :: "+msg+"\n", v...)
	fmt.Printf(fmt.Sprintf("%s:%d", fn, line)+"  :: "+msg+"\n", v...)
}

func Warn(msg string, v ...interface{}) {
	_, fn, line, _ := runtime.Caller(1)
	fmt.Printf(PREFIX_WARN+"  "+getDate()+" "+fmt.Sprintf("%s:%d", fn, line)+" :: "+msg+"\n", v...)
}

func Error(msg string, v ...interface{}) {
	msg = fmt.Sprintf(msg, v...)
	fmt.Printf(" %s  :: %s\n", getDate(), msg)
	if os.Getenv("server") != "localhost" {
		sendMessageToSlack(fmt.Sprintf("#%s  [ERROR] %s  :: %s", strings.ToUpper(os.Getenv("server")), getDate(), msg))
	}
}

func IfError(err error) bool {
	if err != nil {
		_, fn, line, _ := runtime.Caller(1)
		Error("%v \n   at \"%s\", line %d ", err, fn, line)
		return true
	}
	return false
}

func IfErrorSetStatus(ctx *fasthttp.RequestCtx, err error) bool {
	if err != nil {
		ctx.SetContentType("text/plain; charset=utf-8")
		ctx.Response.Header.Set("X-Content-Type-Options", "nosniff")
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)

		_, fn, line, _ := runtime.Caller(1)
		Error("%v \n   at \"%s\", line %d ", err, fn, line)
		return true
	}
	return false
}

func ToSlack(message string) {
	sendMessageToSlack(message)
}

func getDate() string {
	_, offset := time.Now().Zone()
	diff := int64(25200 - offset) //25200 is developer offset
	return time.Unix(time.Now().Unix()+diff, 0).Format("02-01-2006 15:04:05")
}

func sendMessageToSlack(message string) {
	req := request.HttpRequest{}
	req.Method = "POST"
	req.Url = "*******************************************************************************"
	//request.PostRequest.Body = map[string]interface{}{
	//	"text": message,
	//}

	var slackMsg models.SlackMessage
	var slackFields []models.SlackFields
	slackFields = append(slackFields, models.SlackFields{
		Title: "Project",
		Value: fmt.Sprintf("WA BOT - %s", os.Getenv("wa_type")),
		Short: true,
	})

	slackFields = append(slackFields, models.SlackFields{
		Title: "Environment",
		Value: os.Getenv("server"),
		Short: true,
	})

	slackMsg.Attachments = append(slackMsg.Attachments, models.SlackAttachments{
		Fallback: message,
		Text:     message,
		Color:    "#F35A00",
		Fields:   slackFields,
	})
	req.PostRequest.Body = slackMsg

	_, err := req.Execute()
	if err != nil {
		fmt.Println("Sending to slack error ", err)
	}
}
