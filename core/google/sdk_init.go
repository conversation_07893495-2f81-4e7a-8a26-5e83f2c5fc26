package google

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"strings"

	"cloud.google.com/go/profiler"
	"cloud.google.com/go/pubsub"
	"google.golang.org/api/option"
)

var pubsubClient *pubsub.Client

func Profiler() profiler.Config {
	serviceAccountPath := "config/auth/gcloud_service_account.json"
	os.Setenv("GOOGLE_APPLICATION_CREDENTIALS", serviceAccountPath)
	serviceAccount, err := ReadServiceAccount(serviceAccountPath)
	if err != nil {
		fmt.Println("read service account for profile err: ", err)
	}

	cfg := profiler.Config{
		Service:        strings.TrimSpace(fmt.Sprintf("uniq-bot-%v-%v", os.Getenv("wa_type"), os.Getenv("server"))),
		ServiceVersion: "1.0.0",
		ProjectID:      serviceAccount.ProjectID,
	}
	return cfg
}

// read service account (json format) from file
func ReadServiceAccount(keyPath string) (ServiceAccount, error) {
	file, err := os.Open(keyPath)
	if err != nil {
		return ServiceAccount{}, err
	}
	defer file.Close()
	result, err := ioutil.ReadAll(file)
	if err == nil {
		var serviceAccount ServiceAccount
		err = json.Unmarshal(result, &serviceAccount)
		return serviceAccount, err
	}
	return ServiceAccount{}, err
}

func GetPubSubClient() *pubsub.Client {
	if pubsubClient == nil {
		projectId := os.Getenv("PROJECT_ID")
		ctx := context.Background()
		var err error
		// pubsubClient, err = pubsub.NewClient(ctx, projectId)
		pubsubCredPath := "config/auth/pubsub_credential.json"
		serviceAccount, err := ReadServiceAccount(pubsubCredPath)
		if err == nil {
			projectId = serviceAccount.ProjectID
		} else {
			fmt.Printf("failed to read pubsub credential - %v\n", err)
		}
		optCred := option.WithCredentialsFile(pubsubCredPath)
		pubsubClient, err = pubsub.NewClient(ctx, projectId, optCred)
		if err != nil {
			fmt.Println("pubsub.NewClient: ", err)
		}
	}
	return pubsubClient
}
