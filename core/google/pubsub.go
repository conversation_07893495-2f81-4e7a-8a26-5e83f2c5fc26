package google

import (
	"context"
	"fmt"

	"cloud.google.com/go/pubsub"
	"gitlab.com/uniqdev/backend/uniq-bot/core/log"
	"gitlab.com/uniqdev/backend/uniq-bot/core/util/cast"
)

func PublishMessage(data interface{}, topicId string) error {
	ctx := context.Background()
	client := GetPubSubClient()

	if client == nil {
		return fmt.Errorf("client has not been initialized")
	}

	t := client.Topic(topicId)
	result := t.Publish(ctx, &pubsub.Message{
		Data: []byte(cast.ToJson(data)),
	})

	id, err := result.Get(ctx)
	if log.IfError(err) {
		fmt.Printf("failed to publish to topic '%s' : %v", topicId, err)
	}

	fmt.Printf("pubsub published to topic: '%s', msg ID: %v\n", topicId, id)
	return err
}
